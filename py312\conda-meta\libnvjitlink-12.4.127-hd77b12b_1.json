{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnvjitlink-12.4.127-hd77b12b_1", "files": ["Library/bin/nvJitLink_120_0.dll"], "fn": "libnvjitlink-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnvjitlink-12.4.127-hd77b12b_1", "type": 1}, "md5": "2565bc080c8d1eac497a004ec97a8a48", "name": "libnvjitlink", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnvjitlink-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/nvJitLink_120_0.dll", "path_type": "hardlink", "sha256": "2da9da786b85c100ba122f931a49c5c1eda83c32e3fa36814c2ef470e45fa9b1", "sha256_in_prefix": "2da9da786b85c100ba122f931a49c5c1eda83c32e3fa36814c2ef470e45fa9b1", "size_in_bytes": 38247424}], "paths_version": 1}, "requested_spec": "None", "sha256": "718c4bce41d025d78dbdb881256a41206d6494d801efb05b1db65f488297ddaf", "size": 14240031, "subdir": "win-64", "timestamp": 1714771982000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libnvjitlink-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}