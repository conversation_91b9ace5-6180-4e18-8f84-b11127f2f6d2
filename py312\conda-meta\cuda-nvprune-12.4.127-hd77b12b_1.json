{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvprune-12.4.127-hd77b12b_1", "files": ["Library/bin/nvprune.exe"], "fn": "cuda-nvprune-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvprune-12.4.127-hd77b12b_1", "type": 1}, "md5": "5ce777468d3b80f5f4015485ee22a68e", "name": "cuda-nvprune", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvprune-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/nvprune.exe", "path_type": "hardlink", "sha256": "a7c73ce5e8641be507cdf1d1075d047ff28b42c81d00aa8af76b1a23fb653f11", "sha256_in_prefix": "a7c73ce5e8641be507cdf1d1075d047ff28b42c81d00aa8af76b1a23fb653f11", "size_in_bytes": 254464}], "paths_version": 1}, "requested_spec": "None", "sha256": "b4db542fef6b320affee3c891efb9dc0c86bb420a34c710f8f8c8553c4b61145", "size": 158060, "subdir": "win-64", "timestamp": 1715202473000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvprune-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}