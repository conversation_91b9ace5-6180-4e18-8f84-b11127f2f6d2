{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libcufft 11.2.1.3 hd77b12b_1"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcufft-dev-11.2.1.3-hd77b12b_1", "files": ["Library/include/cudalibxt.h", "Library/include/cufft.h", "Library/include/cufftXt.h", "Library/include/cufftw.h", "Library/lib/cufft.lib", "Library/lib/cufftw.lib"], "fn": "libcufft-dev-11.2.1.3-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcufft-dev-11.2.1.3-hd77b12b_1", "type": 1}, "md5": "5283172f373e7c169a38da605182e709", "name": "libcufft-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcufft-dev-11.2.1.3-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/include/cudalibxt.h", "path_type": "hardlink", "sha256": "a1680320630e760cb28223be86f720178b1b9777f1d53638665ab1eebb274591", "sha256_in_prefix": "a1680320630e760cb28223be86f720178b1b9777f1d53638665ab1eebb274591", "size_in_bytes": 4202}, {"_path": "Library/include/cufft.h", "path_type": "hardlink", "sha256": "16652a5a12c4b694e59aa4691a06037ad947e676850f2abda75009bb300e2108", "sha256_in_prefix": "16652a5a12c4b694e59aa4691a06037ad947e676850f2abda75009bb300e2108", "size_in_bytes": 13443}, {"_path": "Library/include/cufftXt.h", "path_type": "hardlink", "sha256": "e216b7f2e7f31f0c8592c3ecd609f47ac3c2a4c6c60e066afd70f2c164cfa402", "sha256_in_prefix": "e216b7f2e7f31f0c8592c3ecd609f47ac3c2a4c6c60e066afd70f2c164cfa402", "size_in_bytes": 11407}, {"_path": "Library/include/cufftw.h", "path_type": "hardlink", "sha256": "64afcc68790a4e46a5b2556ead6502f66ca23ad45ea88ee5b36865b4cf4d335a", "sha256_in_prefix": "64afcc68790a4e46a5b2556ead6502f66ca23ad45ea88ee5b36865b4cf4d335a", "size_in_bytes": 20516}, {"_path": "Library/lib/cufft.lib", "path_type": "hardlink", "sha256": "893dec3394f774f3c01557d7ed7f6b825227f22ff042a73b1b254ada1949d426", "sha256_in_prefix": "893dec3394f774f3c01557d7ed7f6b825227f22ff042a73b1b254ada1949d426", "size_in_bytes": 13620}, {"_path": "Library/lib/cufftw.lib", "path_type": "hardlink", "sha256": "9df7d590730748bda4a87d8bc9475851bf8028f50fbe7a6be1b2866aa41e5d49", "sha256_in_prefix": "9df7d590730748bda4a87d8bc9475851bf8028f50fbe7a6be1b2866aa41e5d49", "size_in_bytes": 17120}], "paths_version": 1}, "requested_spec": "None", "sha256": "5fbe68356081b1ac3ccfcbf0e569864cc4634d779087c3caea187961c563434e", "size": 31785, "subdir": "win-64", "timestamp": 1714677797000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcufft-dev-11.2.1.3-hd77b12b_1.conda", "version": "11.2.1.3"}