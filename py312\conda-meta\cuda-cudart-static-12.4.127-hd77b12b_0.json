{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "cuda-cudart-static_win-64 12.4.127 hd77b12b_0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cudart-static-12.4.127-hd77b12b_0", "files": [], "fn": "cuda-cudart-static-12.4.127-hd77b12b_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cudart-static-12.4.127-hd77b12b_0", "type": 1}, "md5": "46da1450e9ac3a51178ff33c5646b5df", "name": "cuda-cudart-static", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cudart-static-12.4.127-hd77b12b_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "f8f087cd2b36f22206d8eef3a83a82fef11e4ba884fd228ad23cb087bbc19885", "size": 21667, "subdir": "win-64", "timestamp": 1714768748000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-cudart-static-12.4.127-hd77b12b_0.conda", "version": "12.4.127"}