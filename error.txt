F:\Wan2.2\wan2.2-14B-I2V>echo off
ERROR:    [Errno 10048] error while attempting to bind on address ('0.0.0.0', 80): [winerror 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
[START] Security scan
[DONE] Security scan
## ComfyUI-Manager: installing dependencies done.
** ComfyUI startup time: 2025-07-31 15:20:58.594
** Platform: Windows
** Python version: 3.12.7 | packaged by Anaconda, Inc. | (main, Oct  4 2024, 13:17:27) [MSC v.1929 64 bit (AMD64)]
** Python executable: F:\Wan2.2\wan2.2-14B-I2V\py312\\python.exe
** ComfyUI Path: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI
** ComfyUI Base Folder Path: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI
** User directory: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\user
** ComfyUI-Manager config path: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\user\default\ComfyUI-Manager\config.ini
** Log path: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\user\comfyui.log
Exception in thread Thread-5 (start_gradio):
Traceback (most recent call last):
  File "F:\Wan2.2\wan2.2-14B-I2V\py312\Lib\threading.py", line 1075, in _bootstrap_inner
    self.run()
  File "F:\Wan2.2\wan2.2-14B-I2V\py312\Lib\threading.py", line 1012, in run
    self._target(*self._args, **self._kwargs)
  File "cy_app.py", line 397, in cy_app.start_gradio
  File "F:\Wan2.2\wan2.2-14B-I2V\py312\Lib\site-packages\gradio\blocks.py", line 2604, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "F:\Wan2.2\wan2.2-14B-I2V\py312\Lib\site-packages\gradio\http_server.py", line 156, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 80-80. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
请按任意键继续. . .
Prestartup times for custom nodes:
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\rgthree-comfy
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-easy-use
   6.8 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-manager

Checkpoint files will always be loaded safely.
Total VRAM 16376 MB, total RAM 130923 MB
pytorch version: 2.8.0.dev20250414+cu128
Set vram state to: NORMAL_VRAM
Device: cuda:0 NVIDIA GeForce RTX 4080 : cudaMallocAsync
Using pytorch attention
Python version: 3.12.7 | packaged by Anaconda, Inc. | (main, Oct  4 2024, 13:17:27) [MSC v.1929 64 bit (AMD64)]
ComfyUI version: 0.3.46
ComfyUI frontend version: 1.23.4
[Prompt Server] web root: F:\Wan2.2\wan2.2-14B-I2V\py312\Lib\site-packages\comfyui_frontend_package\static
Traceback (most recent call last):
  File "F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\comfy_api_nodes\canary.py", line 5, in <module>
    raise Exception("INSTALL NEW VERSION OF PYAV TO USE API NODES.")
Exception: INSTALL NEW VERSION OF PYAV TO USE API NODES.

Cannot import F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\comfy_api_nodes\canary.py module for custom nodes: INSTALL NEW VERSION OF PYAV TO USE API NODES.
[F:\Wan2_x_2\wan2_x_2-14B-I2V\ComfyUI\custom_nodes\comfy-mtb] | INFO -> loaded 91 nodes successfuly
[F:\Wan2_x_2\wan2_x_2-14B-I2V\ComfyUI\custom_nodes\comfy-mtb] | INFO -> Some nodes (5) could not be loaded. This can be ignored, but go to http://0.0.0.0,:::8188/mtb if you want more information.
[Crystools INFO] Crystools version: 1.22.1
[Crystools INFO] CPU: AMD Ryzen Threadripper PRO 5955WX 16-Cores - Arch: AMD64 - OS: Windows 11
[Crystools INFO] Pynvml (Nvidia) initialized.
[Crystools INFO] GPU/s:
[Crystools INFO] 0) NVIDIA GeForce RTX 4080
[Crystools INFO] NVIDIA Driver: 572.60
[ComfyUI-Easy-Use] server: v1.2.7 Loaded
[ComfyUI-Easy-Use] web root: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 Loaded
ComfyUI-GGUF: Allowing full torch compile (nightly)
### Loading: ComfyUI-Impact-Pack (V8.12)
[WARN] ComfyUI-Impact-Pack: custom_wildcards path not found: E:\work\ltx-video\ComfyUI\custom_nodes\comfyui-impact-pack\custom_wildcards. Using default path.
[Impact Pack] Wildcards loading done.
Total VRAM 16376 MB, total RAM 130923 MB
pytorch version: 2.8.0.dev20250414+cu128
Set vram state to: NORMAL_VRAM
Device: cuda:0 NVIDIA GeForce RTX 4080 : cudaMallocAsync
### Loading: ComfyUI-Manager (V3.32.2)
[ComfyUI-Manager] network_mode: public
### ComfyUI Revision: 3707 [5d4cc3ba] *DETACHED | Released on '2025-07-28'
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-LTXVideo
MultiGPU: Found ComfyUI-LTXVideo, creating compatible MultiGPU nodes
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-Florence2
MultiGPU: Found ComfyUI-Florence2, creating compatible MultiGPU nodes
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI_bitsandbytes_NF4
MultiGPU: Module ComfyUI_bitsandbytes_NF4 not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui_bitsandbytes_nf4
MultiGPU: Module comfyui_bitsandbytes_nf4 not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\x-flux-comfyui
MultiGPU: Module x-flux-comfyui not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\x-flux-comfyui
MultiGPU: Module x-flux-comfyui not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-MMAudio
MultiGPU: Module ComfyUI-MMAudio not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-mmaudio
MultiGPU: Module comfyui-mmaudio not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-GGUF
MultiGPU: Found ComfyUI-GGUF, creating compatible MultiGPU nodes
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\PuLID_ComfyUI
MultiGPU: Module PuLID_ComfyUI not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\pulid_comfyui
MultiGPU: Module pulid_comfyui not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-HunyuanVideoWrapper
MultiGPU: Module ComfyUI-HunyuanVideoWrapper not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-hunyuanvideowrapper
MultiGPU: Module comfyui-hunyuanvideowrapper not found - skipping
MultiGPU: Checking for module at F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
MultiGPU: Found ComfyUI-WanVideoWrapper, creating compatible MultiGPU nodes
MultiGPU: Registration complete. Final mappings: DeviceSelectorMultiGPU, HunyuanVideoEmbeddingsAdapter, MergeFluxLoRAsQuantizeAndLoaddMultiGPU, UNETLoaderMultiGPU, VAELoaderMultiGPU, CLIPLoaderMultiGPU, DualCLIPLoaderMultiGPU, TripleCLIPLoaderMultiGPU, QuadrupleCLIPLoaderMultiGPU, CLIPVisionLoaderMultiGPU, CheckpointLoaderSimpleMultiGPU, ControlNetLoaderMultiGPU, LTXVLoaderMultiGPU, Florence2ModelLoaderMultiGPU, DownloadAndLoadFlorence2ModelMultiGPU, UnetLoaderGGUFMultiGPU, UnetLoaderGGUFDisTorchMultiGPU, UnetLoaderGGUFAdvancedMultiGPU, UnetLoaderGGUFAdvancedDisTorchMultiGPU, CLIPLoaderGGUFMultiGPU, CLIPLoaderGGUFDisTorchMultiGPU, DualCLIPLoaderGGUFMultiGPU, DualCLIPLoaderGGUFDisTorchMultiGPU, TripleCLIPLoaderGGUFMultiGPU, TripleCLIPLoaderGGUFDisTorchMultiGPU, QuadrupleCLIPLoaderGGUFMultiGPU, QuadrupleCLIPLoaderGGUFDisTorchMultiGPU, WanVideoModelLoaderMultiGPU, WanVideoVAELoaderMultiGPU, LoadWanVideoT5TextEncoderMultiGPU
[VideoHelperSuite] - WARNING - Failed to import imageio_ffmpeg
Warning: Could not load sageattention: No module named 'sageattention'
sageattention package is not installed
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
------------------------------------------
Comfyroll Studio v1.76 :  175 Nodes Loaded
------------------------------------------
** For changes, please see patch notes at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/blob/main/Patch_Notes.md
** For help, please see the wiki at https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes/wiki
[ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
------------------------------------------
HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"

[rgthree-comfy] Loaded 42 fantastic nodes. 🎉

FETCH ComfyRegistry Data: 5/93
WAS Node Suite: OpenCV Python FFMPEG support is enabled
WAS Node Suite Warning: `ffmpeg_bin_path` is not set in `F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\was-node-suite-comfyui\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.
WAS Node Suite: Finished. Loaded 220 nodes successfully.

        "Creativity takes courage." - Henri Matisse


Import times for custom nodes:
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\websocket_image_save.py
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-HunyuanVideoMultiLora
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-logic
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-GGUF
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\cg-use-everywhere
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfy-image-saver
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-MediaMixer
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-TeaCache
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-various
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-custom-scripts
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-multigpu
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui_essentials
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-frame-interpolation
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\gguf
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\rgthree-comfy
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-videohelpersuite
   0.0 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-LTXVideo
   0.1 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-kjnodes
   0.1 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-impact-pack
   0.1 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-WanVideoWrapper
   0.2 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI_Comfyroll_CustomNodes
   0.3 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-florence2
   0.6 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfy-mtb
   0.8 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-Crystools
   1.7 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\llm-api
   1.9 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\was-node-suite-comfyui
   2.2 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-manager
   2.4 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui-easy-use
   2.5 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\comfyui_layerstyle
  76.3 seconds: F:\Wan2.2\wan2.2-14B-I2V\ComfyUI\custom_nodes\ComfyUI-Image-Filters

WARNING: some comfy_api_nodes/ nodes did not import correctly. This may be because they are missing some dependencies.

IMPORT FAILED: nodes_ideogram.py
IMPORT FAILED: nodes_openai.py
IMPORT FAILED: nodes_minimax.py
IMPORT FAILED: nodes_veo2.py
IMPORT FAILED: nodes_kling.py
IMPORT FAILED: nodes_bfl.py
IMPORT FAILED: nodes_luma.py
IMPORT FAILED: nodes_recraft.py
IMPORT FAILED: nodes_pixverse.py
IMPORT FAILED: nodes_stability.py
IMPORT FAILED: nodes_pika.py
IMPORT FAILED: nodes_runway.py
IMPORT FAILED: nodes_tripo.py
IMPORT FAILED: nodes_moonvalley.py
IMPORT FAILED: nodes_rodin.py
IMPORT FAILED: nodes_gemini.py

This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
Please do a: pip install -r requirements.txt

Context impl SQLiteImpl.
Will assume non-transactional DDL.
No target revision found.
Starting server

To see the GUI go to: http://0.0.0.0:8188
To see the GUI go to: http://[::]:8188
FETCH ComfyRegistry Data: 10/93
FETCH ComfyRegistry Data: 15/93
FETCH ComfyRegistry Data: 20/93
FETCH ComfyRegistry Data: 25/93
FETCH ComfyRegistry Data: 30/93
FETCH ComfyRegistry Data: 35/93
FETCH ComfyRegistry Data: 40/93
FETCH ComfyRegistry Data: 45/93
FETCH ComfyRegistry Data: 50/93
FETCH ComfyRegistry Data: 55/93
FETCH ComfyRegistry Data: 60/93
FETCH ComfyRegistry Data: 65/93
FETCH ComfyRegistry Data: 70/93
FETCH ComfyRegistry Data: 75/93
FETCH ComfyRegistry Data: 80/93
FETCH ComfyRegistry Data: 85/93
got prompt
Failed to validate prompt for output 9:
* CheckpointLoaderSimple 4:
  - Value not in list: ckpt_name: 'v1-5-pruned-emaonly-fp16.safetensors' not in []
Output will be ignored
invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
FETCH ComfyRegistry Data: 90/93
FETCH ComfyRegistry Data [DONE]
[ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[ComfyUI-Manager] All startup tasks have been completed.
got prompt
Failed to validate prompt for output 9:
* CheckpointLoaderSimple 4:
  - Value not in list: ckpt_name: 'v1-5-pruned-emaonly-fp16.safetensors' not in []
Output will be ignored
invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}