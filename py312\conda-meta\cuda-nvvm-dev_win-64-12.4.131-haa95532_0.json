{"build": "haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/noarch", "constrains": [], "depends": ["cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvvm-dev_win-64-12.4.131-haa95532_0", "files": [], "fn": "cuda-nvvm-dev_win-64-12.4.131-haa95532_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvvm-dev_win-64-12.4.131-haa95532_0", "type": 1}, "md5": "a5eb35998f04bd015849282e7abe365a", "name": "cuda-nvvm-dev_win-64", "noarch": "generic", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvvm-dev_win-64-12.4.131-haa95532_0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "ea0839beca08fb6037133d87df7cc20e4a0cf5c93a2718e72131077aa38c5723", "size": 23805, "subdir": "noarch", "timestamp": 1714770030000, "url": "https://repo.anaconda.com/pkgs/main/noarch/cuda-nvvm-dev_win-64-12.4.131-haa95532_0.conda", "version": "12.4.131"}