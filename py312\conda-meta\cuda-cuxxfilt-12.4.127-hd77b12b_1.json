{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cuxxfilt-12.4.127-hd77b12b_1", "files": ["Library/bin/cu++filt.exe", "Library/include/nv_decode.h", "Library/lib/cufilt.lib"], "fn": "cuda-cuxxfilt-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cuxxfilt-12.4.127-hd77b12b_1", "type": 1}, "md5": "4224c6a82a65c66cbe7c24953d7a21b9", "name": "cuda-cuxxfilt", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cuxxfilt-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/cu++filt.exe", "path_type": "hardlink", "sha256": "9776d6ed2ad119f99d363ccbcd1788be4580c5fbc59cde74258160127020825d", "sha256_in_prefix": "9776d6ed2ad119f99d363ccbcd1788be4580c5fbc59cde74258160127020825d", "size_in_bytes": 202240}, {"_path": "Library/include/nv_decode.h", "path_type": "hardlink", "sha256": "7a69f3d07f81ddc8d487d51a6348d8cdeec44c9be7e465d364401b7ebb621bb7", "sha256_in_prefix": "7a69f3d07f81ddc8d487d51a6348d8cdeec44c9be7e465d364401b7ebb621bb7", "size_in_bytes": 1970}, {"_path": "Library/lib/cufilt.lib", "path_type": "hardlink", "sha256": "14f2c54a70c43f0b469b7853abf6cec649016f240358dae2ee4fe8e435811937", "sha256_in_prefix": "14f2c54a70c43f0b469b7853abf6cec649016f240358dae2ee4fe8e435811937", "size_in_bytes": 157666}], "paths_version": 1}, "requested_spec": "None", "sha256": "137c4fedf98b16ea31f4d8e849a6d5c74271c61343d774507745526b9489cd2a", "size": 164846, "subdir": "win-64", "timestamp": 1715202425000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-cuxxfilt-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}