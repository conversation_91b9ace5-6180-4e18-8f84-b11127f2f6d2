{"build": "h1fd813f_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vs2017_win-64", "cuda-nvcc_win-64 12.4.131.*"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvcc-12.4.131-h1fd813f_0", "files": [], "fn": "cuda-nvcc-12.4.131-h1fd813f_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvcc-12.4.131-h1fd813f_0", "type": 1}, "md5": "3acb711c06a8feb116a72877307068ca", "name": "cuda-nvcc", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvcc-12.4.131-h1fd813f_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "c5a498d6bb95477ab14bf555731062205dfc014006ac6d7b5e08cbdd5dd2960a", "size": 23314, "subdir": "win-64", "timestamp": 1714773591000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvcc-12.4.131-h1fd813f_0.conda", "version": "12.4.131"}