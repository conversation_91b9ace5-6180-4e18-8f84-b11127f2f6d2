{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-nvdisasm", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cuobjdump-12.4.127-hd77b12b_1", "files": ["Library/bin/cuobjdump.exe"], "fn": "cuda-cuobjdump-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cuobjdump-12.4.127-hd77b12b_1", "type": 1}, "md5": "8a19da15d2a9630e9dd6cd4464d84762", "name": "cuda-cuob<PERSON><PERSON><PERSON>", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cuobjdump-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/cuobjdump.exe", "path_type": "hardlink", "sha256": "578644883fbce889a88b5752f3c8616b846f0dac455885cea12a206989bece6a", "sha256_in_prefix": "578644883fbce889a88b5752f3c8616b846f0dac455885cea12a206989bece6a", "size_in_bytes": 11395584}], "paths_version": 1}, "requested_spec": "None", "sha256": "3ce8887e187c7cb9141b8341fc2fa4f1910fc04bbc4d781d228a84819297a0ee", "size": 3947559, "subdir": "win-64", "timestamp": 1715024195000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-cuobjdump-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}