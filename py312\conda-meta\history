==> 2025-03-16 10:48:27 <==
# cmd: D:\anaconda3\Scripts\conda-script.py create -n python312 python=3.12.7
# conda version: 24.9.2
+defaults/noarch::tzdata-2025a-h04d1e81_0
+defaults/win-64::bzip2-1.0.8-h2bbff1b_6
+defaults/win-64::ca-certificates-2025.2.25-haa95532_0
+defaults/win-64::expat-2.6.4-h8ddb27b_0
+defaults/win-64::libffi-3.4.4-hd77b12b_1
+defaults/win-64::openssl-3.0.16-h3f729d1_0
+defaults/win-64::pip-25.0-py312haa95532_0
+defaults/win-64::python-3.12.7-h14ffc60_0
+defaults/win-64::setuptools-75.8.0-py312haa95532_0
+defaults/win-64::sqlite-3.45.3-h2bbff1b_0
+defaults/win-64::tk-8.6.14-h0416ee5_0
+defaults/win-64::vc-14.42-haa95532_4
+defaults/win-64::vs2015_runtime-14.42.34433-he0abc0d_4
+defaults/win-64::wheel-0.45.1-py312haa95532_0
+defaults/win-64::xz-5.6.4-h4754444_1
+defaults/win-64::zlib-1.2.13-h8cc25b3_1
# update specs: ['python=3.12.7']
==> 2025-03-16 10:48:56 <==
# cmd: D:\anaconda3\Scripts\conda-script.py install nvidia/label/cuda-12.4.0::cuda-toolkit
# conda version: 24.9.2
+defaults/noarch::cuda-cccl_win-64-12.4.127-haa95532_2
+defaults/noarch::cuda-crt-dev_win-64-12.4.131-haa95532_0
+defaults/noarch::cuda-cudart-dev_win-64-12.4.127-hd77b12b_0
+defaults/noarch::cuda-cudart-static_win-64-12.4.127-hd77b12b_0
+defaults/noarch::cuda-cudart_win-64-12.4.127-hd77b12b_0
+defaults/noarch::cuda-nvcc-dev_win-64-12.4.131-haa95532_0
+defaults/noarch::cuda-nvvm-dev_win-64-12.4.131-haa95532_0
+defaults/noarch::cuda-version-12.4-hbda6634_3
+defaults/win-64::cccl-2.3.2-h47f531a_0
+defaults/win-64::cuda-cccl-12.4.127-haa95532_2
+defaults/win-64::cuda-command-line-tools-12.4.1-haa95532_1
+defaults/win-64::cuda-compiler-12.4.1-hd77b12b_1
+defaults/win-64::cuda-crt-tools-12.4.131-haa95532_0
+defaults/win-64::cuda-cudart-12.4.127-hd77b12b_0
+defaults/win-64::cuda-cudart-dev-12.4.127-hd77b12b_0
+defaults/win-64::cuda-cudart-static-12.4.127-hd77b12b_0
+defaults/win-64::cuda-cuobjdump-12.4.127-hd77b12b_1
+defaults/win-64::cuda-cupti-12.4.127-hd77b12b_1
+defaults/win-64::cuda-cupti-dev-12.4.127-hd77b12b_1
+defaults/win-64::cuda-cuxxfilt-12.4.127-hd77b12b_1
+defaults/win-64::cuda-libraries-12.4.1-haa95532_1
+defaults/win-64::cuda-libraries-dev-12.4.1-haa95532_1
+defaults/win-64::cuda-nvcc-12.4.131-h1fd813f_0
+defaults/win-64::cuda-nvcc-impl-12.4.131-h35fed64_0
+defaults/win-64::cuda-nvcc-tools-12.4.131-hd77b12b_0
+defaults/win-64::cuda-nvcc_win-64-12.4.131-h1fd813f_0
+defaults/win-64::cuda-nvdisasm-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvml-dev-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvprof-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvprune-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvrtc-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvrtc-dev-12.4.127-hd77b12b_1
+defaults/win-64::cuda-nvvm-impl-12.4.131-hd77b12b_0
+defaults/win-64::cuda-nvvm-tools-12.4.131-hd77b12b_0
+defaults/win-64::cuda-nvvp-12.4.127-hd77b12b_1
+defaults/win-64::cuda-opencl-12.4.127-hd77b12b_0
+defaults/win-64::cuda-opencl-dev-12.4.127-hd77b12b_0
+defaults/win-64::cuda-profiler-api-12.4.127-haa95532_1
+defaults/win-64::cuda-sanitizer-api-12.4.127-hd77b12b_1
+defaults/win-64::cuda-tools-12.4.1-haa95532_1
+defaults/win-64::cuda-visual-tools-12.4.1-haa95532_1
+defaults/win-64::fontconfig-2.14.1-hb33846d_3
+defaults/win-64::freetype-2.12.1-ha860e81_0
+defaults/win-64::khronos-opencl-icd-loader-2024.05.08-h8cc25b3_0
+defaults/win-64::krb5-1.20.1-h5b6d351_0
+defaults/win-64::libcublas-12.4.5.8-hd77b12b_1
+defaults/win-64::libcublas-dev-12.4.5.8-hd77b12b_1
+defaults/win-64::libcufft-11.2.1.3-hd77b12b_1
+defaults/win-64::libcufft-dev-11.2.1.3-hd77b12b_1
+defaults/win-64::libcurand-10.3.5.147-hd77b12b_1
+defaults/win-64::libcurand-dev-10.3.5.147-hd77b12b_1
+defaults/win-64::libcusolver-11.6.1.9-hd77b12b_1
+defaults/win-64::libcusolver-dev-11.6.1.9-hd77b12b_1
+defaults/win-64::libcusparse-12.3.1.170-hd77b12b_1
+defaults/win-64::libcusparse-dev-12.3.1.170-hd77b12b_1
+defaults/win-64::libglib-2.78.4-ha17d25a_0
+defaults/win-64::libiconv-1.16-h2bbff1b_3
+defaults/win-64::libnpp-12.2.5.30-hd77b12b_1
+defaults/win-64::libnpp-dev-12.2.5.30-hd77b12b_1
+defaults/win-64::libnvfatbin-12.4.127-h20ee8b7_2
+defaults/win-64::libnvfatbin-dev-12.4.127-h20ee8b7_2
+defaults/win-64::libnvjitlink-12.4.127-hd77b12b_1
+defaults/win-64::libnvjitlink-dev-12.4.127-hd77b12b_1
+defaults/win-64::libnvjpeg-12.3.1.117-hd77b12b_1
+defaults/win-64::libnvjpeg-dev-12.3.1.117-haa95532_1
+defaults/win-64::libpng-1.6.39-h8cc25b3_0
+defaults/win-64::libxml2-2.13.5-h24da03e_0
+defaults/win-64::nsight-compute-2024.1.1.4-hb5e1e24_2
+defaults/win-64::pcre2-10.42-h0ff8eda_1
+defaults/win-64::vs2017_win-64-19.16.27032.1-hb4161e2_3
+defaults/win-64::vswhere-2.8.4-haa95532_0
+nvidia/label/cuda-12.4.0/win-64::cuda-documentation-12.4.99-0
+nvidia/label/cuda-12.4.0/win-64::cuda-toolkit-12.4.0-0
# update specs: ['nvidia/label/cuda-12.4.0::cuda-toolkit']
==> 2025-03-16 10:49:53 <==
# cmd: D:\anaconda3\Scripts\conda-script.py install -c conda-forge cudnn
# conda version: 24.9.2
-defaults/win-64::openssl-3.0.16-h3f729d1_0
-defaults/win-64::vs2015_runtime-14.42.34433-he0abc0d_4
+conda-forge/win-64::cudnn-********-h1361d0a_0
+conda-forge/win-64::openssl-3.4.1-ha4e3fda_0
+conda-forge/win-64::ucrt-10.0.22621.0-h57928b3_1
+conda-forge/win-64::vc14_runtime-14.42.34438-hfd919c2_24
+conda-forge/win-64::vs2015_runtime-14.42.34438-h7142326_24
# update specs: ['cudnn']
