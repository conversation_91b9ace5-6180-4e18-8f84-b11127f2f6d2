{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": ["libnvjitlink-static >=12.4.127"], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libnvjitlink 12.4.127 hd77b12b_1"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnvjitlink-dev-12.4.127-hd77b12b_1", "files": ["Library/include/nvJitLink.h", "Library/lib/nvJitLink.lib"], "fn": "libnvjitlink-dev-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnvjitlink-dev-12.4.127-hd77b12b_1", "type": 1}, "md5": "91e7763c4659fcbba20db52b59f4fe05", "name": "libnvjitlink-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnvjitlink-dev-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/include/nvJitLink.h", "path_type": "hardlink", "sha256": "5c15689136c1da9a25013118c686e38ceb2adeb2ba544c37d274129fab0c7f06", "sha256_in_prefix": "5c15689136c1da9a25013118c686e38ceb2adeb2ba544c37d274129fab0c7f06", "size_in_bytes": 17018}, {"_path": "Library/lib/nvJitLink.lib", "path_type": "hardlink", "sha256": "d938afec127c0046299b92c7d67277ae129e29ee4fda8ae055b1e1776fc8a14a", "sha256_in_prefix": "d938afec127c0046299b92c7d67277ae129e29ee4fda8ae055b1e1776fc8a14a", "size_in_bytes": 22676}], "paths_version": 1}, "requested_spec": "None", "sha256": "12b4f5c2877e5fe551036056f9a123d81c9a6674c1131daa1a5ca8886cc2346c", "size": 27211, "subdir": "win-64", "timestamp": **********000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libnvjitlink-dev-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}