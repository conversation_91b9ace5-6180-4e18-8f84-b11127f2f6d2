{"build": "haa95532_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-cccl 12.4.127.*", "cuda-cudart-dev 12.4.127.*", "cuda-nvrtc-dev 12.4.127.*", "cuda-opencl-dev 12.4.127.*", "cuda-profiler-api 12.4.127.*", "libcublas-dev 12.4.5.8.*", "libcufft-dev 11.2.1.3.*", "libcurand-dev 10.3.5.147.*", "libcusolver-dev 11.6.1.9.*", "libcusparse-dev 12.3.1.170.*", "libnpp-dev 12.2.5.30.*", "libnvfatbin-dev 12.4.127.*", "libnvjitlink-dev 12.4.127.*", "libnvjpeg-dev 12.3.1.117.*"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-libraries-dev-12.4.1-haa95532_1", "files": [], "fn": "cuda-libraries-dev-12.4.1-haa95532_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-libraries-dev-12.4.1-haa95532_1", "type": 1}, "md5": "c990c7a3bdc31f926199e2c60218eb56", "name": "cuda-libraries-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-libraries-dev-12.4.1-haa95532_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "2c447c25e4671303602def558fab03be71a115a3f1237c56f89c97ba4ea806b6", "size": 19928, "subdir": "win-64", "timestamp": 1716218827000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-libraries-dev-12.4.1-haa95532_1.conda", "version": "12.4.1"}