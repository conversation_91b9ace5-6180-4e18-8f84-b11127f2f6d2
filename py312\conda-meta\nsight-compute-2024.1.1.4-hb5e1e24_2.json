{"build": "hb5e1e24_2", "build_number": 2, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "zlib >=1.2.13,<1.3.0a0", "freetype >=2.10.4,<3.0a0", "cuda-version >=12.4,<12.5.0a0", "krb5 >=1.20.1,<1.21.0a0", "libglib >=2.78.4,<3.0a0", "fontconfig >=2.14.1,<3.0a0", "expat >=2.6.2,<3.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\nsight-compute-2024.1.1.4-hb5e1e24_2", "files": ["Library/nsight-compute/2024.1.1/docs/Archives/index.html", "Library/nsight-compute/2024.1.1/docs/CopyrightAndLicenses/index.html", "Library/nsight-compute/2024.1.1/docs/CustomizationGuide/index.html", "Library/nsight-compute/2024.1.1/docs/Notices/notices.html", "Library/nsight-compute/2024.1.1/docs/NsightCompute/index.html", "Library/nsight-compute/2024.1.1/docs/NsightComputeCli/index.html", "Library/nsight-compute/2024.1.1/docs/NvRulesAPI/index.html", "Library/nsight-compute/2024.1.1/docs/ProfilingGuide/index.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/index.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/gpu-support.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/known-issues.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/library-support-optix.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/library-support.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/platform-support.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/release-notes-older-versions.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/release-notes.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/support.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/system-requirements.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-2.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-3-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-3.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-4.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-5-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-5-2.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-5-3.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-5.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-1-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-1-2.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-2-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-2.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-3-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-3.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-1-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-2.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-3.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-4.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-5.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-6.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-7.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-8.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-9.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-3-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-3.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-1-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-2-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-2.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-3.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-4-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-4.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-1-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-2-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-2-2.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-2.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-3-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-3.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2024-1-1.html", "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2024-1.html", "Library/nsight-compute/2024.1.1/docs/Training/index.html", "Library/nsight-compute/2024.1.1/docs/VERSION", "Library/nsight-compute/2024.1.1/docs/_images/add-remote-connection-private-key.png", "Library/nsight-compute/2024.1.1/docs/_images/add-remote-connection.png", "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-cam.png", "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-display-filter.png", "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-open-button.png", "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-property-filter.png", "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-rendering-options.png", "Library/nsight-compute/2024.1.1/docs/_images/as-viewer.png", "Library/nsight-compute/2024.1.1/docs/_images/baselines-multiple.png", "Library/nsight-compute/2024.1.1/docs/_images/baselines.png", "Library/nsight-compute/2024.1.1/docs/_images/connection-dialog.png", "Library/nsight-compute/2024.1.1/docs/_images/cubin-viewer.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-11.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-12.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-13.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-14.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-15.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-16.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-2.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-3.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-5.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-6.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-7.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-8.png", "Library/nsight-compute/2024.1.1/docs/_images/filter-example-9.png", "Library/nsight-compute/2024.1.1/docs/_images/hw-model-l1tex-ga100-global.png", "Library/nsight-compute/2024.1.1/docs/_images/hw-model-l1tex.png", "Library/nsight-compute/2024.1.1/docs/_images/hw-model-lts-ga100.png", "Library/nsight-compute/2024.1.1/docs/_images/hw-model-lts.png", "Library/nsight-compute/2024.1.1/docs/_images/integration-1.png", "Library/nsight-compute/2024.1.1/docs/_images/integration-2.png", "Library/nsight-compute/2024.1.1/docs/_images/integration-3.png", "Library/nsight-compute/2024.1.1/docs/_images/integration-4.png", "Library/nsight-compute/2024.1.1/docs/_images/main-menu.png", "Library/nsight-compute/2024.1.1/docs/_images/memory-chart-a100.png", "Library/nsight-compute/2024.1.1/docs/_images/memory-peak-mapping.png", "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-dram.png", "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-l1.png", "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-l2-evict-policy.png", "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-l2.png", "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-smem.png", "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-activity.png", "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-from-header.png", "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-from-section.png", "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-gpu-data.png", "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-graphs.png", "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-tables.png", "Library/nsight-compute/2024.1.1/docs/_images/options-profile.png", "Library/nsight-compute/2024.1.1/docs/_images/profile-series-action.png", "Library/nsight-compute/2024.1.1/docs/_images/profile-series-dialog.png", "Library/nsight-compute/2024.1.1/docs/_images/profiled-process.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-header-filter-dialog.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-header.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-callstack.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-details-comments.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-details-source-table.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-fix-column.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-nvtx.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-section-bodies.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-section-rooflines.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-section-with-rule.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-collapse.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-column-chooser.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-heatmap.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-markers.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-profiles-button.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-profiles.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-register-dependencies.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-rel-abs.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-resolve.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-summary-rules.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-summary-table.png", "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-summary.png", "Library/nsight-compute/2024.1.1/docs/_images/progress-log.png", "Library/nsight-compute/2024.1.1/docs/_images/projects-explorer.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-baseline.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-api-stream.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-attach.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-connect.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-connected.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-next-launch.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-profiling-connect.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-profiling-options-sections.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-report.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-rule.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-system-trace-connect.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-system-trace-options.png", "Library/nsight-compute/2024.1.1/docs/_images/quick-start-system-trace-timeline.png", "Library/nsight-compute/2024.1.1/docs/_images/regular-application-process.png", "Library/nsight-compute/2024.1.1/docs/_images/replay-application-kernel-matching.png", "Library/nsight-compute/2024.1.1/docs/_images/replay-application-range.png", "Library/nsight-compute/2024.1.1/docs/_images/replay-application.png", "Library/nsight-compute/2024.1.1/docs/_images/replay-kernel.png", "Library/nsight-compute/2024.1.1/docs/_images/replay-range.png", "Library/nsight-compute/2024.1.1/docs/_images/replay-regular-execution.png", "Library/nsight-compute/2024.1.1/docs/_images/roofline-analysis.png", "Library/nsight-compute/2024.1.1/docs/_images/roofline-overview.png", "Library/nsight-compute/2024.1.1/docs/_images/section-files-2.png", "Library/nsight-compute/2024.1.1/docs/_images/section-files.png", "Library/nsight-compute/2024.1.1/docs/_images/sm-selection-dialog.png", "Library/nsight-compute/2024.1.1/docs/_images/source-comparison-document.png", "Library/nsight-compute/2024.1.1/docs/_images/source-comparison-from-header.png", "Library/nsight-compute/2024.1.1/docs/_images/source-comparison-navigation-buttons.png", "Library/nsight-compute/2024.1.1/docs/_images/source-counters.png", "Library/nsight-compute/2024.1.1/docs/_images/status-banner.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-api-statistics.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-api-stream.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-baselines.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-launch-details.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-metric-details.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-nvtx-resources.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-nvtx.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-resources.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-section-sets.png", "Library/nsight-compute/2024.1.1/docs/_images/tool-window-sections.png", "Library/nsight-compute/2024.1.1/docs/_images/welcome-page.png", "Library/nsight-compute/2024.1.1/docs/_sphinx_design_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "Library/nsight-compute/2024.1.1/docs/_sphinx_design_static/design-tabs.js", "Library/nsight-compute/2024.1.1/docs/_static/NVIDIA-LogoBlack.svg", "Library/nsight-compute/2024.1.1/docs/_static/NVIDIA-LogoWhite.svg", "Library/nsight-compute/2024.1.1/docs/_static/api-styles-dark.css", "Library/nsight-compute/2024.1.1/docs/_static/api-styles.css", "Library/nsight-compute/2024.1.1/docs/_static/basic.css", "Library/nsight-compute/2024.1.1/docs/_static/css/badge_only.css", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/Roboto-Slab-Bold.woff", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/Roboto-Slab-Bold.woff2", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/Roboto-Slab-Regular.woff", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/Roboto-Slab-Regular.woff2", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.eot", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.svg", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.ttf", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.woff", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.woff2", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-bold-italic.woff", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-bold-italic.woff2", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-bold.woff", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-bold.woff2", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-normal-italic.woff", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-normal-italic.woff2", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-normal.woff", "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-normal.woff2", "Library/nsight-compute/2024.1.1/docs/_static/css/theme.css", "Library/nsight-compute/2024.1.1/docs/_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "Library/nsight-compute/2024.1.1/docs/_static/design-tabs.js", "Library/nsight-compute/2024.1.1/docs/_static/doctools.js", "Library/nsight-compute/2024.1.1/docs/_static/documentation_options.js", "Library/nsight-compute/2024.1.1/docs/_static/favicon.ico", "Library/nsight-compute/2024.1.1/docs/_static/file.png", "Library/nsight-compute/2024.1.1/docs/_static/jquery-3.5.1.js", "Library/nsight-compute/2024.1.1/docs/_static/jquery.js", "Library/nsight-compute/2024.1.1/docs/_static/js/badge_only.js", "Library/nsight-compute/2024.1.1/docs/_static/js/html5shiv-printshiv.min.js", "Library/nsight-compute/2024.1.1/docs/_static/js/html5shiv.min.js", "Library/nsight-compute/2024.1.1/docs/_static/js/theme.js", "Library/nsight-compute/2024.1.1/docs/_static/language_data.js", "Library/nsight-compute/2024.1.1/docs/_static/lunr.min.js", "Library/nsight-compute/2024.1.1/docs/_static/lunr_search.js", "Library/nsight-compute/2024.1.1/docs/_static/main_ov_logo_rect.png", "Library/nsight-compute/2024.1.1/docs/_static/main_ov_logo_square.png", "Library/nsight-compute/2024.1.1/docs/_static/mermaid-init.js", "Library/nsight-compute/2024.1.1/docs/_static/minus.png", "Library/nsight-compute/2024.1.1/docs/_static/nsight-compute.ico", "Library/nsight-compute/2024.1.1/docs/_static/nsight-compute.png", "Library/nsight-compute/2024.1.1/docs/_static/omni-style-dark.css", "Library/nsight-compute/2024.1.1/docs/_static/omni-style.css", "Library/nsight-compute/2024.1.1/docs/_static/plus.png", "Library/nsight-compute/2024.1.1/docs/_static/pygments.css", "Library/nsight-compute/2024.1.1/docs/_static/searchtools.js", "Library/nsight-compute/2024.1.1/docs/_static/social-media.js", "Library/nsight-compute/2024.1.1/docs/_static/theme-setter.js", "Library/nsight-compute/2024.1.1/docs/_static/theme-switcher-general.css", "Library/nsight-compute/2024.1.1/docs/_static/twemoji.css", "Library/nsight-compute/2024.1.1/docs/_static/twemoji.js", "Library/nsight-compute/2024.1.1/docs/_static/underscore-1.13.1.js", "Library/nsight-compute/2024.1.1/docs/_static/underscore.js", "Library/nsight-compute/2024.1.1/docs/_static/version.js", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IAction.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IBaseContext.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IContext.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IController.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IEvaluator.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IFrontend.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IMessageVault.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IMetric.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1INvtxDomainInfo.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1INvtxRange.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1INvtxState.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IRange.html", "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1ISourceInfo.html", "Library/nsight-compute/2024.1.1/docs/api/data-structures.html", "Library/nsight-compute/2024.1.1/docs/api/group__NVRULES__HW.html", "Library/nsight-compute/2024.1.1/docs/api/group__NVRULES__LW.html", "Library/nsight-compute/2024.1.1/docs/api/group__NVRULES__NM.html", "Library/nsight-compute/2024.1.1/docs/api/modules.html", "Library/nsight-compute/2024.1.1/docs/api/namespaceNV.html", "Library/nsight-compute/2024.1.1/docs/api/namespaceNV_1_1Rules.html", "Library/nsight-compute/2024.1.1/docs/api/namespaces.html", "Library/nsight-compute/2024.1.1/docs/genindex.html", "Library/nsight-compute/2024.1.1/docs/index.html", "Library/nsight-compute/2024.1.1/docs/objects.inv", "Library/nsight-compute/2024.1.1/docs/project.json", "Library/nsight-compute/2024.1.1/docs/search.html", "Library/nsight-compute/2024.1.1/docs/searchindex.js", "Library/nsight-compute/2024.1.1/extras/FileFormat/CpuStacktrace.proto", "Library/nsight-compute/2024.1.1/extras/FileFormat/Nvtx.proto", "Library/nsight-compute/2024.1.1/extras/FileFormat/NvtxCategories.proto", "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerMetricOptions.proto", "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerReport.proto", "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerReportCommon.proto", "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerResultsCommon.proto", "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerSection.proto", "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerStringTable.proto", "Library/nsight-compute/2024.1.1/extras/FileFormat/RuleResults.proto", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/AdvancedRuleTemplate.py", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/BasicKernelInfo.py", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/BasicRuleTemplate.py", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/KernelInstanceBoundsAnalysis.py", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/KernelInstanceBoundsAnalysis.section", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/RuleTemplate.section", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/RuleTemplate2_table.chart", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/RuleTemplate_bar.chart", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/RuleTemplate_table.chart", "Library/nsight-compute/2024.1.1/extras/RuleTemplates/SpeedupWithFocusMetrics.py", "Library/nsight-compute/2024.1.1/extras/python/_ncu_report.pyd", "Library/nsight-compute/2024.1.1/extras/python/ncu_report.py", "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/README.TXT", "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/instructionMix.cu", "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/instructionMix.pdf", "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/sobelDouble.ncu-rep", "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/sobelFloat.ncu-rep", "Library/nsight-compute/2024.1.1/extras/samples/interKernelCommunication/README.TXT", "Library/nsight-compute/2024.1.1/extras/samples/interKernelCommunication/interKernelCommunication.cu", "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/README.TXT", "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/sharedBankConflicts.cu", "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/sharedBankConflicts.pdf", "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/transposeCoalesced.ncu-rep", "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/transposeNoBankConflicts.ncu-rep", "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/README.TXT", "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/addConstDouble.ncu-rep", "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/addConstDouble3.ncu-rep", "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/uncoalescedGlobalAccesses.cu", "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/uncoalescedGlobalAccesses.pdf", "Library/nsight-compute/2024.1.1/host/target-windows-x64/CudaGpuInfoDumper.exe", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ad10x-gfxt.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ad10x.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga100.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga10b.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga10x-gfxact.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga10x-gfxt.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga10x.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/gh100.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/index.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/tu10x-gfxt.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/tu10x.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/tu11x.config", "Library/nsight-compute/2024.1.1/host/target-windows-x64/NsysVsIntegration.xml", "Library/nsight-compute/2024.1.1/host/target-windows-x64/PythonNvtx/annotations.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjection64.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionHelper64.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionPythonBacktrace64.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionPythonGilTracing64.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionPythonNvtxAnnotations64.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionWindowsHook64.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/arrow.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/bifrost.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/bifrost_loader.2.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/bifrost_plugin.2.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/config.ini", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_100.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_101.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_102.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_110.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_111.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_112.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_113.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_114.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_115.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_116.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_117.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_118.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_124.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/etw_providers_template.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nsight-sys-service.exe", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nsys.exe", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvlog.config.template", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvperf_grfx_host.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvsym.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExt.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtCuda.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtCudaRt.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtOpenCL.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtSync.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtx3.hpp", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImpl.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCore.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCudaRt_v3.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCuda_v3.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplOpenCL_v3.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplSync_v3.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInit.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInitDecls.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInitDefs.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxLinkOnce.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxTypes.h", "Library/nsight-compute/2024.1.1/host/target-windows-x64/parquet.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/bin/python.exe", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/lib/gpustats.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/lib/kernel_helper.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/lib/nsysstats.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/Dockerfile", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/__init__.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/__main__.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/clean.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/data_service.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/format.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/install.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/__init__.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/args.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/collective_loader.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/exceptions.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/heatmap.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/helpers.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_display.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_path.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_pres.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nvtx.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/pace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/recipe.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/recipe_loader.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/summary.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/log.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/nsys_constants.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/pyproject.toml", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/cuda_api_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/analysis.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/cuda_api_sync.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/cuda_gpu_kern_pace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/pace.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/cuda_gpu_kern_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/cuda_gpu_mem_size_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/cuda_gpu_mem_time_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/cuda_gpu_time_util_map.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/heatmap.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/analysis.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/cuda_memcpy_async.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/analysis.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/cuda_memcpy_sync.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/analysis.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/cuda_memset_sync.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/diff.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/diff.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/analysis.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/dx12_mem_ops.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/analysis.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/gpu_gaps.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/gpu_metric_util_map.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/heatmap.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/analysis.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/gpu_time_util.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/heatmap.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/mpi_gpu_time_util_map.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/mpi_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/nccl_gpu_proj_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/heatmap.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/nccl_gpu_time_util_map.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/nccl_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nic_metric_map/heatmap.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nic_metric_map/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nic_metric_map/nic_metric_map.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/nvtx_gpu_proj_pace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/pace.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/nvtx_gpu_proj_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/nvtx_gpu_proj_trace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/trace.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/nvtx_pace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/pace.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/nvtx_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/osrt_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/stats.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/heatmap.ipynb", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/metadata.json", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/ucx_gpu_time_util_map.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/common.txt", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/dask.txt", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/jupyter.txt", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/_sqlite3.cpython-310-x86_64-linux-gnu.so", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/__init__.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/dbapi2.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/dump.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/README.txt", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/_sql.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/_sqlfile.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/_tbl.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/_values.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/apigpusum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_api_gpu_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_api_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_api_trace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_kern_gb_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_kern_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_mem_size_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_mem_time_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_trace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_kern_exec_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_kern_exec_trace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cudaapisum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cudaapitrace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx11_pix_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx11pixsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx12_gpu_marker_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx12_pix_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx12gpumarkersum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx12pixsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpukerngbsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpukernsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpumemsizesum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpumemtimesum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpusum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gputrace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/kernexecsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/kernexectrace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/khrdebuggpusum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/khrdebugsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/mpi_event_trace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/network_congestion.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_gpu_proj_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_gpu_proj_trace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_kern_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_pushpop_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_pushpop_trace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_startend_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxgpuproj.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxkernsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxppsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxpptrace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxsesum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxsssum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvvideo_api_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/openacc_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/openaccsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/opengl_khr_gpu_range_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/opengl_khr_range_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/openmp_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/openmpevtsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/osrt_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/osrtsum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/um_cpu_page_faults_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/um_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/um_total_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/umcpupagefaults.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/unifiedmemory.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/unifiedmemorytotals.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkan_api_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkan_api_trace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkan_gpu_marker_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkan_marker_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkanapisum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkanapitrace.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkangpumarkersum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkanmarkerssum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/wddm_queue_sum.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/wddmqueuesdetails.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/README.txt", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda-async-memcpy.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda-sync-api.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda-sync-memcpy.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda-sync-memset.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda_api_sync.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda_memcpy_async.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda_memcpy_sync.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda_memset_sync.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/dx12-mem-op.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/dx12_mem_ops.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/gpu-low-util.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/gpu-starv.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/gpu_gaps.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/gpu_time_util.py", "Library/nsight-compute/2024.1.1/host/target-windows-x64/sqlite3.dll", "Library/nsight-compute/2024.1.1/host/target-windows-x64/sqlite3.exe", "Library/nsight-compute/2024.1.1/host/target-windows-x64/targetsettings.xml", "Library/nsight-compute/2024.1.1/host/target-windows-x64/vulkan-layers/VkLayer_nsight-sys_windows.json", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AgentAPI.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Analysis.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AnalysisContainersData.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AnalysisData.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AnalysisProto.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AppLib.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AppLibInterfaces.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Assert.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/CommonProtoServices.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/CommonProtoStreamSections.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Core.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/CrashReporter.exe", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/CudaDrvApiWrapper.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/DeviceProperty.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/DevicePropertyProto.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ETWEventsHandlers.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/EventSource.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/EventsView.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ExternalIntegration.xml", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/GenericHierarchy.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/GpuInfo.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/GpuTraits.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/HostCommon.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InjectionCommunicator.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceData.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceShared.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceSharedBase.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceSharedCore.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceSharedLoggers.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/NvLog.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/NvQtGui.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/NvmlWrapper.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/NvtxExtData.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/CorePlugin/CorePlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/CorePlugin/Manifest.js", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/ExternalIntegrationPlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/LinuxPlatformPlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/Manifest.js", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/QuadDPlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/default.layout", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/RebelPlugin/RebelPlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/SassDebuggerPlugin/SassDebuggerPlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/TPSConnectionPlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/TPSSystemServerPlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/TimelinePlugin/Manifest.js", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/TimelinePlugin/TimelinePlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/WindowsPlatformPlugin.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qgif.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qico.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qjpeg.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qsvg.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qtga.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qtiff.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qwbmp.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/platforms/qwindows.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/tls/qcertonlybackend.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/tls/qopensslbackend.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ProcessLauncher.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ProtobufComm.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ProtobufCommClient.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ProtobufCommProto.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QdstrmImporter.exe", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Charts.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Concurrent.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Core.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Designer.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6DesignerComponents.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Gui.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Help.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Multimedia.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6MultimediaQuick.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6MultimediaWidgets.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Network.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6OpenGL.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6OpenGLWidgets.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Positioning.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6PrintSupport.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Qml.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6QmlModels.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Quick.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6QuickParticles.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6QuickTest.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6QuickWidgets.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Sensors.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Sql.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6StateMachine.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Svg.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6SvgWidgets.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Test.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6UiTools.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6WebChannel.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6WebEngineCore.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6WebEngineWidgets.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6WebSockets.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Widgets.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Xml.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QtPropertyBrowser.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QtWebEngineProcess.exe", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QuiverContainers.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QuiverEvents.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/SshClient.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/StreamSections.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/SymbolAnalyzerLight.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/SymbolDemangler.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TelemetryQuadDClient.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TimelineAssert.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TimelineCommon.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TimelineUIUtils.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TimelineWidget.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-file-l1-2-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-file-l2-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-localization-l1-2-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-processthreads-l1-1-1.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-synch-l1-2-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-timezone-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-convert-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-environment-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-filesystem-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-heap-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-locale-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-math-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-multibyte-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-runtime-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-stdio-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-string-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-time-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-utility-l1-1-0.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/arrow.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_atomic-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_chrono-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_container-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_date_time-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_filesystem-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_iostreams-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_locale-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_program_options-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_python310-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_regex-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_serialization-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_system-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_thread-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_timer-vc141-mt-x64-1_78.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/exporter.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/libcrypto-3-x64.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/libssl-3-x64.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/msdia140.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ncu-ui.exe", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/nvsym.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/opengl32sw.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/parquet.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/protobuf-shared.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/qt.conf", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/icudtl.dat", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_devtools_resources.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources_100p.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources_200p.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/sqlite3.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ssh.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/am.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ar.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/bg.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/bn.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ca.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/cs.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/da.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/de.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/el.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/en-GB.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/en-US.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/es-419.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/es.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/et.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fa.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fi.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fil.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fr.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/gu.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/he.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hi.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hr.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hu.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/id.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/it.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ja.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/kn.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ko.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/lt.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/lv.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ml.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/mr.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ms.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/nb.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/nl.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pl.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pt-BR.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pt-PT.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ro.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ru.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sk.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sl.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sr.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sv.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sw.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ta.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/te.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/th.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/tr.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/uk.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/vi.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/zh-CN.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/zh-TW.pak", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ucrtbase.dll", "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/zlib.dll", "Library/nsight-compute/2024.1.1/ncu-ui.bat", "Library/nsight-compute/2024.1.1/ncu.bat", "Library/nsight-compute/2024.1.1/sections/AchievedOccupancy.py", "Library/nsight-compute/2024.1.1/sections/C2CLink.section", "Library/nsight-compute/2024.1.1/sections/CPIStall.py", "Library/nsight-compute/2024.1.1/sections/Compute.py", "Library/nsight-compute/2024.1.1/sections/ComputeWorkloadAnalysis.section", "Library/nsight-compute/2024.1.1/sections/FPInstructions.py", "Library/nsight-compute/2024.1.1/sections/HighPipeUtilization.py", "Library/nsight-compute/2024.1.1/sections/InstructionStatistics.section", "Library/nsight-compute/2024.1.1/sections/IssueSlotUtilization.py", "Library/nsight-compute/2024.1.1/sections/LaunchStatistics.py", "Library/nsight-compute/2024.1.1/sections/LaunchStatistics.section", "Library/nsight-compute/2024.1.1/sections/Memory.py", "Library/nsight-compute/2024.1.1/sections/MemoryApertureUsage.py", "Library/nsight-compute/2024.1.1/sections/MemoryCacheAccessPattern.py", "Library/nsight-compute/2024.1.1/sections/MemoryL2Compression.py", "Library/nsight-compute/2024.1.1/sections/MemoryWorkloadAnalysis.section", "Library/nsight-compute/2024.1.1/sections/MemoryWorkloadAnalysis_Chart.section", "Library/nsight-compute/2024.1.1/sections/MemoryWorkloadAnalysis_Tables.section", "Library/nsight-compute/2024.1.1/sections/NumaAffinity.section", "Library/nsight-compute/2024.1.1/sections/NvRules.py", "Library/nsight-compute/2024.1.1/sections/Nvlink.section", "Library/nsight-compute/2024.1.1/sections/Nvlink_Tables.section", "Library/nsight-compute/2024.1.1/sections/Nvlink_Topology.section", "Library/nsight-compute/2024.1.1/sections/Occupancy.section", "Library/nsight-compute/2024.1.1/sections/PCSamplingData.py", "Library/nsight-compute/2024.1.1/sections/PmSampling.section", "Library/nsight-compute/2024.1.1/sections/PmSampling_WarpStates.section", "Library/nsight-compute/2024.1.1/sections/RequestedMetrics.py", "Library/nsight-compute/2024.1.1/sections/SchedulerStatistics.section", "Library/nsight-compute/2024.1.1/sections/SharedMemoryConflicts.py", "Library/nsight-compute/2024.1.1/sections/SlowPipeLimiter.py", "Library/nsight-compute/2024.1.1/sections/SourceCounters.section", "Library/nsight-compute/2024.1.1/sections/SpeedOfLight.py", "Library/nsight-compute/2024.1.1/sections/SpeedOfLight.section", "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_HierarchicalDoubleRooflineChart.section", "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_HierarchicalHalfRooflineChart.section", "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_HierarchicalSingleRooflineChart.section", "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_HierarchicalTensorRooflineChart.section", "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_Roofline.py", "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_RooflineChart.section", "Library/nsight-compute/2024.1.1/sections/TheoreticalOccupancy.py", "Library/nsight-compute/2024.1.1/sections/ThreadDivergence.py", "Library/nsight-compute/2024.1.1/sections/UncoalescedAccess.chart", "Library/nsight-compute/2024.1.1/sections/UncoalescedAccess.py", "Library/nsight-compute/2024.1.1/sections/UncoalescedSharedAccess.chart", "Library/nsight-compute/2024.1.1/sections/UncoalescedSharedAccess.py", "Library/nsight-compute/2024.1.1/sections/WarpStateStatistics.section", "Library/nsight-compute/2024.1.1/sections/WorkloadDistribution.section", "Library/nsight-compute/2024.1.1/sections/WorkloadImbalance.py", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/TreeLauncherSubreaper", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/TreeLauncherTargetLdPreloadHelper", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libInterceptorInjectionTarget.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherPlaceholder.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherTargetInjection.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherTargetUpdatePreloadInjection.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libcuda-injection.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libnvperf_host.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libnvperf_target.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/ncu", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/TreeLauncherTargetLdPreloadHelper", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/libInterceptorInjectionTarget.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherPlaceholder.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherTargetInjection.so", "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherTargetUpdatePreloadInjection.so", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/InterceptorInjectionTarget.dll", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/TreeLauncherTargetInjection.dll", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/cuda-injection.dll", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/icudt71.dll", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/icuuc71.dll", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/ncu.exe", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/nvperf_host.dll", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/nvperf_target.dll", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x86/InterceptorInjectionTarget.dll", "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x86/TreeLauncherTargetInjection.dll", "Scripts/CrashReporter.bat", "Scripts/CudaGpuInfoDumper.bat", "Scripts/QdstrmImporter.bat", "Scripts/QtWebEngineProcess.bat", "Scripts/ncu-ui.bat", "Scripts/ncu.bat", "Scripts/nsight-sys-service.bat", "Scripts/nsys.bat", "Scripts/python.bat", "Scripts/sqlite3.bat"], "fn": "nsight-compute-2024.1.1.4-hb5e1e24_2.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\nsight-compute-2024.1.1.4-hb5e1e24_2", "type": 1}, "md5": "babbac2150a2eb177d537e97ee9c2a8d", "name": "nsight-compute", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\nsight-compute-2024.1.1.4-hb5e1e24_2.conda", "paths_data": {"paths": [{"_path": "Library/nsight-compute/2024.1.1/docs/Archives/index.html", "path_type": "hardlink", "sha256": "fa75485e467149d6c1a0e6499d69683f1763af824c4ce7b5ad3b3028e503bfe8", "sha256_in_prefix": "fa75485e467149d6c1a0e6499d69683f1763af824c4ce7b5ad3b3028e503bfe8", "size_in_bytes": 10889}, {"_path": "Library/nsight-compute/2024.1.1/docs/CopyrightAndLicenses/index.html", "path_type": "hardlink", "sha256": "61ad041c8983de6c4b9b6769e9f01ac7af0d61176b7c48eb718e1304df205d67", "sha256_in_prefix": "61ad041c8983de6c4b9b6769e9f01ac7af0d61176b7c48eb718e1304df205d67", "size_in_bytes": 129299}, {"_path": "Library/nsight-compute/2024.1.1/docs/CustomizationGuide/index.html", "path_type": "hardlink", "sha256": "7b86659198cfab3d500a024dad58169945cee4872d8147f2d0ff54c15e860d23", "sha256_in_prefix": "7b86659198cfab3d500a024dad58169945cee4872d8147f2d0ff54c15e860d23", "size_in_bytes": 71055}, {"_path": "Library/nsight-compute/2024.1.1/docs/Notices/notices.html", "path_type": "hardlink", "sha256": "cf07830268773d1b1dee7ef2600835fc991c169ecbae4c780fcc8783167e8e9c", "sha256_in_prefix": "cf07830268773d1b1dee7ef2600835fc991c169ecbae4c780fcc8783167e8e9c", "size_in_bytes": 6844}, {"_path": "Library/nsight-compute/2024.1.1/docs/NsightCompute/index.html", "path_type": "hardlink", "sha256": "4806c9215b3a8a12bf18091eff0407a4aea95b13dcead68ce9e7f5635ad398d2", "sha256_in_prefix": "4806c9215b3a8a12bf18091eff0407a4aea95b13dcead68ce9e7f5635ad398d2", "size_in_bytes": 205216}, {"_path": "Library/nsight-compute/2024.1.1/docs/NsightComputeCli/index.html", "path_type": "hardlink", "sha256": "4234a8dc2f595ed5bbf686ccad1e30de7246a8aaa8a6ee237e6448ac2cfc6ab3", "sha256_in_prefix": "4234a8dc2f595ed5bbf686ccad1e30de7246a8aaa8a6ee237e6448ac2cfc6ab3", "size_in_bytes": 156481}, {"_path": "Library/nsight-compute/2024.1.1/docs/NvRulesAPI/index.html", "path_type": "hardlink", "sha256": "589db1657f22eea328c932909e79e924015f3d13eb3e8e2289c0cb35a0cbac97", "sha256_in_prefix": "589db1657f22eea328c932909e79e924015f3d13eb3e8e2289c0cb35a0cbac97", "size_in_bytes": 7944}, {"_path": "Library/nsight-compute/2024.1.1/docs/ProfilingGuide/index.html", "path_type": "hardlink", "sha256": "20c6c0f18ddaa8f6f7ecbfec07d251cf449f24e4bc79246c76d1d7c9f054aca2", "sha256_in_prefix": "20c6c0f18ddaa8f6f7ecbfec07d251cf449f24e4bc79246c76d1d7c9f054aca2", "size_in_bytes": 247427}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/index.html", "path_type": "hardlink", "sha256": "b08e0bd0ba3c1095e29cb921113c8b8ca18b6365031c49444dd994cc561ab374", "sha256_in_prefix": "b08e0bd0ba3c1095e29cb921113c8b8ca18b6365031c49444dd994cc561ab374", "size_in_bytes": 147368}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/gpu-support.html", "path_type": "hardlink", "sha256": "6edf985add964006b4a5b9bca7a3454eb161a58d561c3f6fe4f7735ccc73308a", "sha256_in_prefix": "6edf985add964006b4a5b9bca7a3454eb161a58d561c3f6fe4f7735ccc73308a", "size_in_bytes": 7302}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/known-issues.html", "path_type": "hardlink", "sha256": "9aff79252a39bd95020528316734b898d8a59a0e4f2f5bd39b79c8afd2b46e75", "sha256_in_prefix": "9aff79252a39bd95020528316734b898d8a59a0e4f2f5bd39b79c8afd2b46e75", "size_in_bytes": 19871}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/library-support-optix.html", "path_type": "hardlink", "sha256": "dba6132fda43e094ffd03fbb8dd3a70ad60cd1c8d4e87825c0df5b15c1e33aa8", "sha256_in_prefix": "dba6132fda43e094ffd03fbb8dd3a70ad60cd1c8d4e87825c0df5b15c1e33aa8", "size_in_bytes": 8108}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/library-support.html", "path_type": "hardlink", "sha256": "24f84c8f2a820100912bcd133bb17d0bad8f9a19f4e722ca1178cfec90661eab", "sha256_in_prefix": "24f84c8f2a820100912bcd133bb17d0bad8f9a19f4e722ca1178cfec90661eab", "size_in_bytes": 6012}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/platform-support.html", "path_type": "hardlink", "sha256": "72da3fe12a8f32424deb45da0c432d452e71f46a8f4425cdc6d99f8ca48ba796", "sha256_in_prefix": "72da3fe12a8f32424deb45da0c432d452e71f46a8f4425cdc6d99f8ca48ba796", "size_in_bytes": 7901}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/release-notes-older-versions.html", "path_type": "hardlink", "sha256": "217dcfcc1f6ef787299ec8dde215fa6fd512a3a9874c8a0ebb5900ae151b7107", "sha256_in_prefix": "217dcfcc1f6ef787299ec8dde215fa6fd512a3a9874c8a0ebb5900ae151b7107", "size_in_bytes": 5602}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/release-notes.html", "path_type": "hardlink", "sha256": "8385de6c2e9d1271d7e33e84839f1c59f6e8ca86fd0c406d42181b5514971127", "sha256_in_prefix": "8385de6c2e9d1271d7e33e84839f1c59f6e8ca86fd0c406d42181b5514971127", "size_in_bytes": 5597}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/support.html", "path_type": "hardlink", "sha256": "a464ad4f8c658bec6261d4828e23415a8c1f91cf0e7b2fb9956f08bb2af3ef5d", "sha256_in_prefix": "a464ad4f8c658bec6261d4828e23415a8c1f91cf0e7b2fb9956f08bb2af3ef5d", "size_in_bytes": 5619}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/system-requirements.html", "path_type": "hardlink", "sha256": "3bb5a6557ea15812e26768b3d9fd0ea3abb69a10f852a913e693618b8062e806", "sha256_in_prefix": "3bb5a6557ea15812e26768b3d9fd0ea3abb69a10f852a913e693618b8062e806", "size_in_bytes": 9411}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-1.html", "path_type": "hardlink", "sha256": "36c6db32d8150f6b2394e37e04ef881c8e318e39834c282e235206ac993e64fd", "sha256_in_prefix": "36c6db32d8150f6b2394e37e04ef881c8e318e39834c282e235206ac993e64fd", "size_in_bytes": 9054}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-2.html", "path_type": "hardlink", "sha256": "f481407ad4cdb2155741a449d9df39aee3972ce6b69180ff870b96576d5e82ee", "sha256_in_prefix": "f481407ad4cdb2155741a449d9df39aee3972ce6b69180ff870b96576d5e82ee", "size_in_bytes": 8041}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-3-1.html", "path_type": "hardlink", "sha256": "e932f3886b1c590e6eacc91ccc98fc52484a29885f765701c48c8e9a3e6711e2", "sha256_in_prefix": "e932f3886b1c590e6eacc91ccc98fc52484a29885f765701c48c8e9a3e6711e2", "size_in_bytes": 7376}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-3.html", "path_type": "hardlink", "sha256": "c769bc8f4e2d937bd02d7dc7a00774da5d532edfb4554459cc08ff2e1dbe5917", "sha256_in_prefix": "c769bc8f4e2d937bd02d7dc7a00774da5d532edfb4554459cc08ff2e1dbe5917", "size_in_bytes": 8041}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-4.html", "path_type": "hardlink", "sha256": "e00693ab57e5dd532ad60baeb769089b0d3a6407292d4fd7598e9251436f6afd", "sha256_in_prefix": "e00693ab57e5dd532ad60baeb769089b0d3a6407292d4fd7598e9251436f6afd", "size_in_bytes": 9345}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-5-1.html", "path_type": "hardlink", "sha256": "5e060a3dab65ccbf70618e8a7fa276b05e3db8fea2808a7917a54b929bb5f73a", "sha256_in_prefix": "5e060a3dab65ccbf70618e8a7fa276b05e3db8fea2808a7917a54b929bb5f73a", "size_in_bytes": 5760}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-5-2.html", "path_type": "hardlink", "sha256": "cb0fd150a7465ec1d950319b781382a1adf7550b23665a6d4d4ba7fe3a13d2d9", "sha256_in_prefix": "cb0fd150a7465ec1d950319b781382a1adf7550b23665a6d4d4ba7fe3a13d2d9", "size_in_bytes": 5711}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-5-3.html", "path_type": "hardlink", "sha256": "a09db3ebb4918e7cd7d4a9c9ec31cb47098c15bcefb0f5a1175120f63c5cfd51", "sha256_in_prefix": "a09db3ebb4918e7cd7d4a9c9ec31cb47098c15bcefb0f5a1175120f63c5cfd51", "size_in_bytes": 5749}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2019-5.html", "path_type": "hardlink", "sha256": "6f1b389d30731566410480a5f3e3bedf3651278f2dbae2366afccc9ab4d004e6", "sha256_in_prefix": "6f1b389d30731566410480a5f3e3bedf3651278f2dbae2366afccc9ab4d004e6", "size_in_bytes": 8719}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-1-1.html", "path_type": "hardlink", "sha256": "ea3785549cded9d50e7451f927dd078f99d34ab5bec9459cf92ac3010b93edbf", "sha256_in_prefix": "ea3785549cded9d50e7451f927dd078f99d34ab5bec9459cf92ac3010b93edbf", "size_in_bytes": 7616}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-1-2.html", "path_type": "hardlink", "sha256": "cdc7ef5eea9722d5033a571984055130696db06af3bcd7947713e047461ffaff", "sha256_in_prefix": "cdc7ef5eea9722d5033a571984055130696db06af3bcd7947713e047461ffaff", "size_in_bytes": 6350}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-1.html", "path_type": "hardlink", "sha256": "cc1cdb1d9400b10997dc1ddb2df36d01001040f38ba7d456bf468b761e841945", "sha256_in_prefix": "cc1cdb1d9400b10997dc1ddb2df36d01001040f38ba7d456bf468b761e841945", "size_in_bytes": 10057}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-2-1.html", "path_type": "hardlink", "sha256": "3c81371bfff83e75a1b545efbe356f60f3aae5e7ebe63b9c4f5e5e9a698fc5c5", "sha256_in_prefix": "3c81371bfff83e75a1b545efbe356f60f3aae5e7ebe63b9c4f5e5e9a698fc5c5", "size_in_bytes": 6712}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-2.html", "path_type": "hardlink", "sha256": "efc2bea7a9b0bca00d8eb7b00f5cdcf67985875c16e57f45b1abd6d5ac7ff79d", "sha256_in_prefix": "efc2bea7a9b0bca00d8eb7b00f5cdcf67985875c16e57f45b1abd6d5ac7ff79d", "size_in_bytes": 8652}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-3-1.html", "path_type": "hardlink", "sha256": "3056d2d91a054cff8d2dd4e742a0607acfd4e37e69ac6319845b5a771cbf08b8", "sha256_in_prefix": "3056d2d91a054cff8d2dd4e742a0607acfd4e37e69ac6319845b5a771cbf08b8", "size_in_bytes": 6983}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2020-3.html", "path_type": "hardlink", "sha256": "d0abcc9f58a3a2d9d1a06f4e4b1e4a055ba08d17e1f433f1d3acee434c35360d", "sha256_in_prefix": "d0abcc9f58a3a2d9d1a06f4e4b1e4a055ba08d17e1f433f1d3acee434c35360d", "size_in_bytes": 10370}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-1-1.html", "path_type": "hardlink", "sha256": "4fd71ffd2952e017705ee50a7e3fa4ccdf70c017eade9af8128e4ef66b96f567", "sha256_in_prefix": "4fd71ffd2952e017705ee50a7e3fa4ccdf70c017eade9af8128e4ef66b96f567", "size_in_bytes": 7901}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-1.html", "path_type": "hardlink", "sha256": "de30fcc4e673211f20ad991d84980afa851c427a04cec2f43d10d6f47cb3372a", "sha256_in_prefix": "de30fcc4e673211f20ad991d84980afa851c427a04cec2f43d10d6f47cb3372a", "size_in_bytes": 8863}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-1.html", "path_type": "hardlink", "sha256": "ee2c252fbe13a74e397842adc0b54af1ef64788a29659aae51ec768b63a63dc4", "sha256_in_prefix": "ee2c252fbe13a74e397842adc0b54af1ef64788a29659aae51ec768b63a63dc4", "size_in_bytes": 6268}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-2.html", "path_type": "hardlink", "sha256": "7dde1711c554732e5b572d0bd0876ee503070bb7c179b168dab48e40a92ecdea", "sha256_in_prefix": "7dde1711c554732e5b572d0bd0876ee503070bb7c179b168dab48e40a92ecdea", "size_in_bytes": 6079}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-3.html", "path_type": "hardlink", "sha256": "cee50371fc8d5feee34906d58659ec86d36aba6fbe2514ca8678de4095fcc83f", "sha256_in_prefix": "cee50371fc8d5feee34906d58659ec86d36aba6fbe2514ca8678de4095fcc83f", "size_in_bytes": 5907}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-4.html", "path_type": "hardlink", "sha256": "9b1cec15308a8a3777cc48138bef30df4548ec8e44aece62cfbcbcc14c2b24c4", "sha256_in_prefix": "9b1cec15308a8a3777cc48138bef30df4548ec8e44aece62cfbcbcc14c2b24c4", "size_in_bytes": 5802}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-5.html", "path_type": "hardlink", "sha256": "4d3841a5aa06cf47b777280d6b41e43833ab153cb34c7f2a7fa82a3ec1269fd3", "sha256_in_prefix": "4d3841a5aa06cf47b777280d6b41e43833ab153cb34c7f2a7fa82a3ec1269fd3", "size_in_bytes": 5792}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-6.html", "path_type": "hardlink", "sha256": "89167342242c10e70381b32fd2a38899db01266ca28f80e62bb3696ab6d10158", "sha256_in_prefix": "89167342242c10e70381b32fd2a38899db01266ca28f80e62bb3696ab6d10158", "size_in_bytes": 5888}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-7.html", "path_type": "hardlink", "sha256": "bf9af3b06ccfc7f036b38cec1d38ed99b12ea4e5e2d38f4576d631a61c1ab2f3", "sha256_in_prefix": "bf9af3b06ccfc7f036b38cec1d38ed99b12ea4e5e2d38f4576d631a61c1ab2f3", "size_in_bytes": 5766}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-8.html", "path_type": "hardlink", "sha256": "f2b7b22733dc56d6bf153047366c200ba2ded73de1419fc3ba0fc7b6d66d0b87", "sha256_in_prefix": "f2b7b22733dc56d6bf153047366c200ba2ded73de1419fc3ba0fc7b6d66d0b87", "size_in_bytes": 5745}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2-9.html", "path_type": "hardlink", "sha256": "6168c8683a5a0e4b45864d30eeed300726252e57bc6f343570d065704fd9bae4", "sha256_in_prefix": "6168c8683a5a0e4b45864d30eeed300726252e57bc6f343570d065704fd9bae4", "size_in_bytes": 5807}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-2.html", "path_type": "hardlink", "sha256": "40516f24ea9857484770c1a79072af096c868cd9fccfd9bcfe917a9669210eac", "sha256_in_prefix": "40516f24ea9857484770c1a79072af096c868cd9fccfd9bcfe917a9669210eac", "size_in_bytes": 11153}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-3-1.html", "path_type": "hardlink", "sha256": "10f67c21a7326dbbe90cc486c547e62e5248d9643805e2f8aabdc8f19cf7b412", "sha256_in_prefix": "10f67c21a7326dbbe90cc486c547e62e5248d9643805e2f8aabdc8f19cf7b412", "size_in_bytes": 6398}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2021-3.html", "path_type": "hardlink", "sha256": "e68b71972f6b0ce02ebb3ce0a52121ad4c20c85126f63f825f25cf22ae825b14", "sha256_in_prefix": "e68b71972f6b0ce02ebb3ce0a52121ad4c20c85126f63f825f25cf22ae825b14", "size_in_bytes": 10184}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-1-1.html", "path_type": "hardlink", "sha256": "346cc1414478ac6179229376fd2341d01738709f1d9acea921f6b5c6a01d6ead", "sha256_in_prefix": "346cc1414478ac6179229376fd2341d01738709f1d9acea921f6b5c6a01d6ead", "size_in_bytes": 7188}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-1.html", "path_type": "hardlink", "sha256": "91fbe041f963dedf335e66ea418e75ea808574fc5df8f3fe8f47c2baa180ac46", "sha256_in_prefix": "91fbe041f963dedf335e66ea418e75ea808574fc5df8f3fe8f47c2baa180ac46", "size_in_bytes": 9540}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-2-1.html", "path_type": "hardlink", "sha256": "e6d2192e1ecae1b4f93fb4e25f0bf1d6c2c397ee2b325d8cb95f5091698ae394", "sha256_in_prefix": "e6d2192e1ecae1b4f93fb4e25f0bf1d6c2c397ee2b325d8cb95f5091698ae394", "size_in_bytes": 6358}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-2.html", "path_type": "hardlink", "sha256": "7e29ad8ec4923124df99daa462773b7fbe3199cabd820020de3ddac9ad474a07", "sha256_in_prefix": "7e29ad8ec4923124df99daa462773b7fbe3199cabd820020de3ddac9ad474a07", "size_in_bytes": 10447}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-3.html", "path_type": "hardlink", "sha256": "6e1cbe14c1a42125abbf1afabd41e6504036dd00ab24f87ea7049296c09d926a", "sha256_in_prefix": "6e1cbe14c1a42125abbf1afabd41e6504036dd00ab24f87ea7049296c09d926a", "size_in_bytes": 9687}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-4-1.html", "path_type": "hardlink", "sha256": "0ea066ccd1fc67abac624be9a9422f295331bf0b17f60852e881770c2076878b", "sha256_in_prefix": "0ea066ccd1fc67abac624be9a9422f295331bf0b17f60852e881770c2076878b", "size_in_bytes": 6308}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2022-4.html", "path_type": "hardlink", "sha256": "b94e48395c37d4ce0f45da5eaec8ae553c5329ec86a26d0feb6042d20ce4d53f", "sha256_in_prefix": "b94e48395c37d4ce0f45da5eaec8ae553c5329ec86a26d0feb6042d20ce4d53f", "size_in_bytes": 12047}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-1-1.html", "path_type": "hardlink", "sha256": "5199fb827e15e2adecf409f7e97b069a255f9a3596cddebd5628f89b7aacf94c", "sha256_in_prefix": "5199fb827e15e2adecf409f7e97b069a255f9a3596cddebd5628f89b7aacf94c", "size_in_bytes": 7840}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-1.html", "path_type": "hardlink", "sha256": "2d5747b7a30c807f80d13c7ac444eeb5e37b90b56fe155fcd056862006b68eef", "sha256_in_prefix": "2d5747b7a30c807f80d13c7ac444eeb5e37b90b56fe155fcd056862006b68eef", "size_in_bytes": 10811}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-2-1.html", "path_type": "hardlink", "sha256": "83608467378ffd2e1a0c1320c52b36064165538f1ca57ee444ec278468b975cc", "sha256_in_prefix": "83608467378ffd2e1a0c1320c52b36064165538f1ca57ee444ec278468b975cc", "size_in_bytes": 5954}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-2-2.html", "path_type": "hardlink", "sha256": "1315ab1cb20dbf4ef4b38ce898fc90c5650a1385159d9743019896162c321bd0", "sha256_in_prefix": "1315ab1cb20dbf4ef4b38ce898fc90c5650a1385159d9743019896162c321bd0", "size_in_bytes": 6057}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-2.html", "path_type": "hardlink", "sha256": "77a4f881abe863714743656f64754ef01b9f72998e7c982e70ae5ea2a70c3dd1", "sha256_in_prefix": "77a4f881abe863714743656f64754ef01b9f72998e7c982e70ae5ea2a70c3dd1", "size_in_bytes": 9411}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-3-1.html", "path_type": "hardlink", "sha256": "78b2808362a841389ee309cf9b61c382856bae6a00d3407ba7f7298188cc2719", "sha256_in_prefix": "78b2808362a841389ee309cf9b61c382856bae6a00d3407ba7f7298188cc2719", "size_in_bytes": 7055}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2023-3.html", "path_type": "hardlink", "sha256": "dc1d1a6933dab43d94fc50027f17e9aab26a088f7d639da87718e399815b875c", "sha256_in_prefix": "dc1d1a6933dab43d94fc50027f17e9aab26a088f7d639da87718e399815b875c", "size_in_bytes": 10760}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2024-1-1.html", "path_type": "hardlink", "sha256": "afe4329683729b17e92260569ab62b4060a807d19c47fcf6207faa0481a1151e", "sha256_in_prefix": "afe4329683729b17e92260569ab62b4060a807d19c47fcf6207faa0481a1151e", "size_in_bytes": 6546}, {"_path": "Library/nsight-compute/2024.1.1/docs/ReleaseNotes/topics/updates-2024-1.html", "path_type": "hardlink", "sha256": "38a47a3347e8ffd375459aef134b7e12483ceb238c865848c6985898d8913d54", "sha256_in_prefix": "38a47a3347e8ffd375459aef134b7e12483ceb238c865848c6985898d8913d54", "size_in_bytes": 9525}, {"_path": "Library/nsight-compute/2024.1.1/docs/Training/index.html", "path_type": "hardlink", "sha256": "ad67e05cd4f9bbdb8bdb1934131f9dc6fded53b36d6e809efcc3130bec0843c2", "sha256_in_prefix": "ad67e05cd4f9bbdb8bdb1934131f9dc6fded53b36d6e809efcc3130bec0843c2", "size_in_bytes": 16453}, {"_path": "Library/nsight-compute/2024.1.1/docs/VERSION", "path_type": "hardlink", "sha256": "8514a481264b3954e99bdaf7f62ebecfa15b2e91d292440ca3341c60725c22cb", "sha256_in_prefix": "8514a481264b3954e99bdaf7f62ebecfa15b2e91d292440ca3341c60725c22cb", "size_in_bytes": 5}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/add-remote-connection-private-key.png", "path_type": "hardlink", "sha256": "de7b7f1e6d5563cde878f464a1710ed9bcf2f1a76350acee9f84fa7d583c65e0", "sha256_in_prefix": "de7b7f1e6d5563cde878f464a1710ed9bcf2f1a76350acee9f84fa7d583c65e0", "size_in_bytes": 32245}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/add-remote-connection.png", "path_type": "hardlink", "sha256": "c316c611cb24b5813f713424616f8ffc42c5e2907bd02ba6e978ccc1900d9ece", "sha256_in_prefix": "c316c611cb24b5813f713424616f8ffc42c5e2907bd02ba6e978ccc1900d9ece", "size_in_bytes": 29244}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-cam.png", "path_type": "hardlink", "sha256": "0c44ebb00bbfd1bac6b8e2802b1655d552a02bc08827cee78f56af0ac0c2699d", "sha256_in_prefix": "0c44ebb00bbfd1bac6b8e2802b1655d552a02bc08827cee78f56af0ac0c2699d", "size_in_bytes": 29258}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-display-filter.png", "path_type": "hardlink", "sha256": "6e97758b342f391273311e8e5f212272ff8d7c3125ffd0d92564c9a9f7545453", "sha256_in_prefix": "6e97758b342f391273311e8e5f212272ff8d7c3125ffd0d92564c9a9f7545453", "size_in_bytes": 40531}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-open-button.png", "path_type": "hardlink", "sha256": "459a207becec2003d7dd312a76ede4c393cba4f4dc2dd0c47c1fbb044da2b926", "sha256_in_prefix": "459a207becec2003d7dd312a76ede4c393cba4f4dc2dd0c47c1fbb044da2b926", "size_in_bytes": 32173}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-property-filter.png", "path_type": "hardlink", "sha256": "bc4a4bee8528de24eec2f1bf9e9d47dbefbc514ddf9f2452ad3bade2227f5692", "sha256_in_prefix": "bc4a4bee8528de24eec2f1bf9e9d47dbefbc514ddf9f2452ad3bade2227f5692", "size_in_bytes": 13188}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/as-viewer-rendering-options.png", "path_type": "hardlink", "sha256": "2673534e36e906c921a62e2a7eda0eedfedba39bf5d7a8d11c827f10e291c817", "sha256_in_prefix": "2673534e36e906c921a62e2a7eda0eedfedba39bf5d7a8d11c827f10e291c817", "size_in_bytes": 17085}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/as-viewer.png", "path_type": "hardlink", "sha256": "04a7c168a4eb0066c3ca1ac1c62d1e157a6c7c857fed75e389c3129f6542c615", "sha256_in_prefix": "04a7c168a4eb0066c3ca1ac1c62d1e157a6c7c857fed75e389c3129f6542c615", "size_in_bytes": 607821}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/baselines-multiple.png", "path_type": "hardlink", "sha256": "d4f49ccd48221d4bb022329d66511eca92d1b33725fbd83f15088fb074338378", "sha256_in_prefix": "d4f49ccd48221d4bb022329d66511eca92d1b33725fbd83f15088fb074338378", "size_in_bytes": 63852}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/baselines.png", "path_type": "hardlink", "sha256": "2b841afc34a7ed962cf47155cfeb0d151a86e56f2a08e98f0b67aa518f8fb53b", "sha256_in_prefix": "2b841afc34a7ed962cf47155cfeb0d151a86e56f2a08e98f0b67aa518f8fb53b", "size_in_bytes": 57622}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/connection-dialog.png", "path_type": "hardlink", "sha256": "d6a5fc5d767b99a603b0439e2e6deeeadc3f2d2ebf3322a366d945cc17ab0e12", "sha256_in_prefix": "d6a5fc5d767b99a603b0439e2e6deeeadc3f2d2ebf3322a366d945cc17ab0e12", "size_in_bytes": 31806}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/cubin-viewer.png", "path_type": "hardlink", "sha256": "a6b1fd5a948556a20c3b7cc60ace06fcab261469f40e13249393f45fe5f464aa", "sha256_in_prefix": "a6b1fd5a948556a20c3b7cc60ace06fcab261469f40e13249393f45fe5f464aa", "size_in_bytes": 60926}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-11.png", "path_type": "hardlink", "sha256": "1bc89a4d53ddb1896f05d6e2b2986e602f8d6611182530103d9b174e64735ecc", "sha256_in_prefix": "1bc89a4d53ddb1896f05d6e2b2986e602f8d6611182530103d9b174e64735ecc", "size_in_bytes": 16609}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-12.png", "path_type": "hardlink", "sha256": "97a6ccbadb5fcd999e5cb5b2e95d3b0b52c96637bd377b843f5ceccc518028cf", "sha256_in_prefix": "97a6ccbadb5fcd999e5cb5b2e95d3b0b52c96637bd377b843f5ceccc518028cf", "size_in_bytes": 15005}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-13.png", "path_type": "hardlink", "sha256": "ebf289807dde357b3073ffa0a9d54f777bccbdbdd42ff73bf81a0c6016491df7", "sha256_in_prefix": "ebf289807dde357b3073ffa0a9d54f777bccbdbdd42ff73bf81a0c6016491df7", "size_in_bytes": 22352}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-14.png", "path_type": "hardlink", "sha256": "22131ced2ba0034b020d15eda3a7438c1e7e9f35c504a6d8c3088d29b1ff71d0", "sha256_in_prefix": "22131ced2ba0034b020d15eda3a7438c1e7e9f35c504a6d8c3088d29b1ff71d0", "size_in_bytes": 19416}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-15.png", "path_type": "hardlink", "sha256": "20210f5550f397b8befd0485881e79a04e8128076e815d6b3826ea7f80a898d6", "sha256_in_prefix": "20210f5550f397b8befd0485881e79a04e8128076e815d6b3826ea7f80a898d6", "size_in_bytes": 35160}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-16.png", "path_type": "hardlink", "sha256": "161c0deadabea3df1d5e7763c009c3ada8b88a646c6bce12dac1f1eb14dcdb73", "sha256_in_prefix": "161c0deadabea3df1d5e7763c009c3ada8b88a646c6bce12dac1f1eb14dcdb73", "size_in_bytes": 32152}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-2.png", "path_type": "hardlink", "sha256": "b405e8b942581b67d6a094b97577180ef622db06cf5ecc69a76ff706830a606f", "sha256_in_prefix": "b405e8b942581b67d6a094b97577180ef622db06cf5ecc69a76ff706830a606f", "size_in_bytes": 19687}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-3.png", "path_type": "hardlink", "sha256": "18e4aa3f07bc5d328902190e6e8db0e731ed58ce1ada787caa8f4222bc87f711", "sha256_in_prefix": "18e4aa3f07bc5d328902190e6e8db0e731ed58ce1ada787caa8f4222bc87f711", "size_in_bytes": 20414}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-5.png", "path_type": "hardlink", "sha256": "3bb4debdae0262faa4c6c26399293197df0b567ba97a3fb645188d286e7c31c4", "sha256_in_prefix": "3bb4debdae0262faa4c6c26399293197df0b567ba97a3fb645188d286e7c31c4", "size_in_bytes": 10367}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-6.png", "path_type": "hardlink", "sha256": "edd8c656bdd9e11db0fe7badf0cf31ffe57a5bc632781632cde52c6e5402b488", "sha256_in_prefix": "edd8c656bdd9e11db0fe7badf0cf31ffe57a5bc632781632cde52c6e5402b488", "size_in_bytes": 11725}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-7.png", "path_type": "hardlink", "sha256": "a95cfc343197afa2b9ac1144783ef3a86df1f977ada195d4c52eb6f6541cae27", "sha256_in_prefix": "a95cfc343197afa2b9ac1144783ef3a86df1f977ada195d4c52eb6f6541cae27", "size_in_bytes": 10594}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-8.png", "path_type": "hardlink", "sha256": "8a05c81e08a3f6333bd777f346bd3d99b59cc5efa8ab50b26c60aceb0e47ba72", "sha256_in_prefix": "8a05c81e08a3f6333bd777f346bd3d99b59cc5efa8ab50b26c60aceb0e47ba72", "size_in_bytes": 12922}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/filter-example-9.png", "path_type": "hardlink", "sha256": "1bee0a5700143ea6e71356f284dc6c03b8b15448ff00692abbd52eab761f1f08", "sha256_in_prefix": "1bee0a5700143ea6e71356f284dc6c03b8b15448ff00692abbd52eab761f1f08", "size_in_bytes": 12205}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/hw-model-l1tex-ga100-global.png", "path_type": "hardlink", "sha256": "dd4407b3e325f56779a26799d661f2452d74d487257e02d5b7f5301dda22afed", "sha256_in_prefix": "dd4407b3e325f56779a26799d661f2452d74d487257e02d5b7f5301dda22afed", "size_in_bytes": 293093}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/hw-model-l1tex.png", "path_type": "hardlink", "sha256": "80d78ea525a7b4390fea36fae6b32fb3d75f0ad51d27225f6bc5eb38961099cc", "sha256_in_prefix": "80d78ea525a7b4390fea36fae6b32fb3d75f0ad51d27225f6bc5eb38961099cc", "size_in_bytes": 98145}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/hw-model-lts-ga100.png", "path_type": "hardlink", "sha256": "c51f43da9a5c587bee8ec778091b6849feb1543dd42a34c5224a664e542e07ba", "sha256_in_prefix": "c51f43da9a5c587bee8ec778091b6849feb1543dd42a34c5224a664e542e07ba", "size_in_bytes": 246445}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/hw-model-lts.png", "path_type": "hardlink", "sha256": "8550103de558005351d4b9f0af748fef3277afec57a4daae6bebb978ed942e36", "sha256_in_prefix": "8550103de558005351d4b9f0af748fef3277afec57a4daae6bebb978ed942e36", "size_in_bytes": 57637}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/integration-1.png", "path_type": "hardlink", "sha256": "32f851b64b744b0f71fb5e266694da5f23ebd50fce3c78df38c4bc25db4d2968", "sha256_in_prefix": "32f851b64b744b0f71fb5e266694da5f23ebd50fce3c78df38c4bc25db4d2968", "size_in_bytes": 44337}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/integration-2.png", "path_type": "hardlink", "sha256": "ec075c2056ba16b9893d28ba1b992231a0f01bf4c030ad7395d518f6a52bb81b", "sha256_in_prefix": "ec075c2056ba16b9893d28ba1b992231a0f01bf4c030ad7395d518f6a52bb81b", "size_in_bytes": 56408}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/integration-3.png", "path_type": "hardlink", "sha256": "fa326a7c70f402bc7b45bd05d1250185f7c954290adaf1848603b55ca82821aa", "sha256_in_prefix": "fa326a7c70f402bc7b45bd05d1250185f7c954290adaf1848603b55ca82821aa", "size_in_bytes": 60613}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/integration-4.png", "path_type": "hardlink", "sha256": "f3aa5e0b63265f13fb4fd5389343c5ab3c3f0da29bb85bf2849af1ff25ad530b", "sha256_in_prefix": "f3aa5e0b63265f13fb4fd5389343c5ab3c3f0da29bb85bf2849af1ff25ad530b", "size_in_bytes": 15366}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/main-menu.png", "path_type": "hardlink", "sha256": "dd922cf8a19df9b7942fac3284b13a0ab2a9b3d9847ef7bafc01305f2ec46ed5", "sha256_in_prefix": "dd922cf8a19df9b7942fac3284b13a0ab2a9b3d9847ef7bafc01305f2ec46ed5", "size_in_bytes": 6138}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/memory-chart-a100.png", "path_type": "hardlink", "sha256": "1286eddd9d5383982d204c5d2e664104855dea244ae7d12196ab62171661f2d3", "sha256_in_prefix": "1286eddd9d5383982d204c5d2e664104855dea244ae7d12196ab62171661f2d3", "size_in_bytes": 57914}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/memory-peak-mapping.png", "path_type": "hardlink", "sha256": "c662c12e526e63e35964334747ae77e23b2778a8924edc1e9a18039992342af1", "sha256_in_prefix": "c662c12e526e63e35964334747ae77e23b2778a8924edc1e9a18039992342af1", "size_in_bytes": 250299}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-dram.png", "path_type": "hardlink", "sha256": "e67860a8f8f2237f9fa42b6a084196ac73978054098a026fd587f9ead2888e51", "sha256_in_prefix": "e67860a8f8f2237f9fa42b6a084196ac73978054098a026fd587f9ead2888e51", "size_in_bytes": 21651}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-l1.png", "path_type": "hardlink", "sha256": "c431cd8fa03c4ff77f71c05f75459b815b3eaf3a4ee9688273133f0f51585100", "sha256_in_prefix": "c431cd8fa03c4ff77f71c05f75459b815b3eaf3a4ee9688273133f0f51585100", "size_in_bytes": 44148}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-l2-evict-policy.png", "path_type": "hardlink", "sha256": "5624bb49c5d9916eda73e861cced16e8fbdc99073325a05933130f5ea3bebdf0", "sha256_in_prefix": "5624bb49c5d9916eda73e861cced16e8fbdc99073325a05933130f5ea3bebdf0", "size_in_bytes": 16177}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-l2.png", "path_type": "hardlink", "sha256": "64a3a2e300523bf4d749463f634648a209ade20517320f7ca76ab5f0ed4d3cff", "sha256_in_prefix": "64a3a2e300523bf4d749463f634648a209ade20517320f7ca76ab5f0ed4d3cff", "size_in_bytes": 30922}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/memory-tables-smem.png", "path_type": "hardlink", "sha256": "ded7953c038abe412e73b4ea92e356bf8d65b58116ef089aaf7c52bf9143906e", "sha256_in_prefix": "ded7953c038abe412e73b4ea92e356bf8d65b58116ef089aaf7c52bf9143906e", "size_in_bytes": 11049}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-activity.png", "path_type": "hardlink", "sha256": "cc1d6c90ac8460c5bb98812fa8f4ad7fccd1e11f15a3991508bdaa0c17253294", "sha256_in_prefix": "cc1d6c90ac8460c5bb98812fa8f4ad7fccd1e11f15a3991508bdaa0c17253294", "size_in_bytes": 22840}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-from-header.png", "path_type": "hardlink", "sha256": "c6bc08e313a447e29fbb71a87de469aaf0b266219f1ad2c91ebefe7d0c2d2935", "sha256_in_prefix": "c6bc08e313a447e29fbb71a87de469aaf0b266219f1ad2c91ebefe7d0c2d2935", "size_in_bytes": 4915}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-from-section.png", "path_type": "hardlink", "sha256": "653f03e7173da6eb2be9681bb99f1999d3bca0d11ef292760e7cab7cc0403bd6", "sha256_in_prefix": "653f03e7173da6eb2be9681bb99f1999d3bca0d11ef292760e7cab7cc0403bd6", "size_in_bytes": 17106}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-gpu-data.png", "path_type": "hardlink", "sha256": "0642993aa63952bcd4d43912c6d7a09e8723bba539d6b1df826daa03386a612e", "sha256_in_prefix": "0642993aa63952bcd4d43912c6d7a09e8723bba539d6b1df826daa03386a612e", "size_in_bytes": 66975}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-graphs.png", "path_type": "hardlink", "sha256": "ee9956cf35f725d8b1ae417bbe9ee4f227861645969223af426301014dd14cc1", "sha256_in_prefix": "ee9956cf35f725d8b1ae417bbe9ee4f227861645969223af426301014dd14cc1", "size_in_bytes": 59475}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/occupancy-calculator-tables.png", "path_type": "hardlink", "sha256": "fd8933627b9d6b6756889d558cbf7b46be1bdd6aad29e609346f0aa11e8ed177", "sha256_in_prefix": "fd8933627b9d6b6756889d558cbf7b46be1bdd6aad29e609346f0aa11e8ed177", "size_in_bytes": 82984}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/options-profile.png", "path_type": "hardlink", "sha256": "6225d41fd8a02dc680286a7cc0ddc2bb31abb54ff667d05f2f00b7c5daa2f420", "sha256_in_prefix": "6225d41fd8a02dc680286a7cc0ddc2bb31abb54ff667d05f2f00b7c5daa2f420", "size_in_bytes": 38184}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profile-series-action.png", "path_type": "hardlink", "sha256": "c1955b9e05f144a3eb38c83f2c81f116df0314d704121986488d561632839446", "sha256_in_prefix": "c1955b9e05f144a3eb38c83f2c81f116df0314d704121986488d561632839446", "size_in_bytes": 9199}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profile-series-dialog.png", "path_type": "hardlink", "sha256": "b921293a1a7c8b72b7454de09e44be03203b2ff30c203dbb4195c39202a4ab46", "sha256_in_prefix": "b921293a1a7c8b72b7454de09e44be03203b2ff30c203dbb4195c39202a4ab46", "size_in_bytes": 20064}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiled-process.png", "path_type": "hardlink", "sha256": "9be19b19ff44cdbf0354de19cf24b8686a6a3b930f18f68e57a9e5d03d3e4abb", "sha256_in_prefix": "9be19b19ff44cdbf0354de19cf24b8686a6a3b930f18f68e57a9e5d03d3e4abb", "size_in_bytes": 34816}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-header-filter-dialog.png", "path_type": "hardlink", "sha256": "e3550d9a2699262d8d94b865d338c4ccb3525681ce2be0ecf261baba75986c55", "sha256_in_prefix": "e3550d9a2699262d8d94b865d338c4ccb3525681ce2be0ecf261baba75986c55", "size_in_bytes": 14477}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-header.png", "path_type": "hardlink", "sha256": "8c4731056adab9d3f55e3c9a09c80b6b8783998f209bb067bf1b97a1b584d254", "sha256_in_prefix": "8c4731056adab9d3f55e3c9a09c80b6b8783998f209bb067bf1b97a1b584d254", "size_in_bytes": 76459}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-callstack.png", "path_type": "hardlink", "sha256": "9bdbacf9abbcc3e4fd5a6007ddc469df13cb4e2b4e97c890b6f0104eac451445", "sha256_in_prefix": "9bdbacf9abbcc3e4fd5a6007ddc469df13cb4e2b4e97c890b6f0104eac451445", "size_in_bytes": 13492}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-details-comments.png", "path_type": "hardlink", "sha256": "1a7a7233a2a84f1b64809a82c0b2a1a9e06004119f562f4ce00f2c731c7fdc53", "sha256_in_prefix": "1a7a7233a2a84f1b64809a82c0b2a1a9e06004119f562f4ce00f2c731c7fdc53", "size_in_bytes": 2764}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-details-source-table.png", "path_type": "hardlink", "sha256": "8ec13c87d1c37a34b780c79cc8d181e4536b89e67d81e422b30d4271d460a063", "sha256_in_prefix": "8ec13c87d1c37a34b780c79cc8d181e4536b89e67d81e422b30d4271d460a063", "size_in_bytes": 55263}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-fix-column.png", "path_type": "hardlink", "sha256": "fcc99cfb2e557570a67714d43627635a5153a06768a408b256fda540755dd999", "sha256_in_prefix": "fcc99cfb2e557570a67714d43627635a5153a06768a408b256fda540755dd999", "size_in_bytes": 4381}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-nvtx.png", "path_type": "hardlink", "sha256": "139952bc012c32bc1c33bdf6befcd51d007ed52161fdf6ec547fe06e1e0bdafe", "sha256_in_prefix": "139952bc012c32bc1c33bdf6befcd51d007ed52161fdf6ec547fe06e1e0bdafe", "size_in_bytes": 5645}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-section-bodies.png", "path_type": "hardlink", "sha256": "5ae3b89f871cf29b9cef3631ffbc45d6b5c7fcae93d87699bdbf6c5c26fd4704", "sha256_in_prefix": "5ae3b89f871cf29b9cef3631ffbc45d6b5c7fcae93d87699bdbf6c5c26fd4704", "size_in_bytes": 31235}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-section-rooflines.png", "path_type": "hardlink", "sha256": "975d796c1376af819a77680b1970f92e6617745b51b50e746b34f01210a2bb12", "sha256_in_prefix": "975d796c1376af819a77680b1970f92e6617745b51b50e746b34f01210a2bb12", "size_in_bytes": 61741}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-section-with-rule.png", "path_type": "hardlink", "sha256": "667305963d2cc0485135c0241e1f7ff4979337c507e31c807b9c63915528af39", "sha256_in_prefix": "667305963d2cc0485135c0241e1f7ff4979337c507e31c807b9c63915528af39", "size_in_bytes": 44386}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-collapse.png", "path_type": "hardlink", "sha256": "f8b95351bac5cba7f34f2c4e71282f9f631bf6d5a16d68c5c6b94eba32d1f7c5", "sha256_in_prefix": "f8b95351bac5cba7f34f2c4e71282f9f631bf6d5a16d68c5c6b94eba32d1f7c5", "size_in_bytes": 82765}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-column-chooser.png", "path_type": "hardlink", "sha256": "650642ce8c866908c4e41be7f4c22ac7168668a99757a7b50077e26f8777bcae", "sha256_in_prefix": "650642ce8c866908c4e41be7f4c22ac7168668a99757a7b50077e26f8777bcae", "size_in_bytes": 42229}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-heatmap.png", "path_type": "hardlink", "sha256": "511de68dd3a0cf25de7bebfd6ae2ef833d2070d62de3a06ebce3bcbc7384d035", "sha256_in_prefix": "511de68dd3a0cf25de7bebfd6ae2ef833d2070d62de3a06ebce3bcbc7384d035", "size_in_bytes": 1127}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-markers.png", "path_type": "hardlink", "sha256": "9fb7dc57c7cd45743da6eeb6fae438064a127d658812ac9be24c4980a32f0400", "sha256_in_prefix": "9fb7dc57c7cd45743da6eeb6fae438064a127d658812ac9be24c4980a32f0400", "size_in_bytes": 55152}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-profiles-button.png", "path_type": "hardlink", "sha256": "3d362c29b394f78a2feb9f10f8d32927f059961a036a0dcd37322a42d5db03a1", "sha256_in_prefix": "3d362c29b394f78a2feb9f10f8d32927f059961a036a0dcd37322a42d5db03a1", "size_in_bytes": 19282}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-profiles.png", "path_type": "hardlink", "sha256": "462e78db1bc43b3b2e7b88d5fa56805933de25d7ac63684a4362ea8d4ccfc0ed", "sha256_in_prefix": "462e78db1bc43b3b2e7b88d5fa56805933de25d7ac63684a4362ea8d4ccfc0ed", "size_in_bytes": 5438}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-register-dependencies.png", "path_type": "hardlink", "sha256": "13a5da7327be895d0894cbb7baa465a038624777344faff28ded90d04abdda20", "sha256_in_prefix": "13a5da7327be895d0894cbb7baa465a038624777344faff28ded90d04abdda20", "size_in_bytes": 73431}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-rel-abs.png", "path_type": "hardlink", "sha256": "82b2b365f4d47b5448047fa1649c8f8e737da54fa5ddbf626c1d4a0d4baebf2a", "sha256_in_prefix": "82b2b365f4d47b5448047fa1649c8f8e737da54fa5ddbf626c1d4a0d4baebf2a", "size_in_bytes": 138306}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source-resolve.png", "path_type": "hardlink", "sha256": "a9c1b6659ed4cc25eb992f94a65847b3f511d5aff97f8cab9981ea5a0013279a", "sha256_in_prefix": "a9c1b6659ed4cc25eb992f94a65847b3f511d5aff97f8cab9981ea5a0013279a", "size_in_bytes": 64438}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-source.png", "path_type": "hardlink", "sha256": "fb51ec908282c9b9be3dcd5de717f23768366aa245649b4c3eb271ff4d2ee944", "sha256_in_prefix": "fb51ec908282c9b9be3dcd5de717f23768366aa245649b4c3eb271ff4d2ee944", "size_in_bytes": 315499}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-summary-rules.png", "path_type": "hardlink", "sha256": "0776a83c8ffcb25398e514b43bf9247370647e010465168b206ab8b96fedfb2d", "sha256_in_prefix": "0776a83c8ffcb25398e514b43bf9247370647e010465168b206ab8b96fedfb2d", "size_in_bytes": 177513}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-summary-table.png", "path_type": "hardlink", "sha256": "04299f219d8fde6bb2ee58f12ee8142f140ff76e989d8c6409d67be0e68fcd54", "sha256_in_prefix": "04299f219d8fde6bb2ee58f12ee8142f140ff76e989d8c6409d67be0e68fcd54", "size_in_bytes": 91896}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/profiler-report-pages-summary.png", "path_type": "hardlink", "sha256": "b626e87dff6a7629963a10327cf6494d60e5aad5aec218153a03a97fa4a42fc4", "sha256_in_prefix": "b626e87dff6a7629963a10327cf6494d60e5aad5aec218153a03a97fa4a42fc4", "size_in_bytes": 537176}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/progress-log.png", "path_type": "hardlink", "sha256": "6ca5ddeebc088bae2c162662a804d822709fa9d55f6d9f1dd02cbdf9b5471c5d", "sha256_in_prefix": "6ca5ddeebc088bae2c162662a804d822709fa9d55f6d9f1dd02cbdf9b5471c5d", "size_in_bytes": 57158}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/projects-explorer.png", "path_type": "hardlink", "sha256": "c37b74207f10d37049a2f3d9d5dff31103d63eac6c29ceeeae5fe7d2c502f590", "sha256_in_prefix": "c37b74207f10d37049a2f3d9d5dff31103d63eac6c29ceeeae5fe7d2c502f590", "size_in_bytes": 5408}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-baseline.png", "path_type": "hardlink", "sha256": "40d1979abb178cda2315f4a5c67326f72367c1427ace430da4022bd0744c4c58", "sha256_in_prefix": "40d1979abb178cda2315f4a5c67326f72367c1427ace430da4022bd0744c4c58", "size_in_bytes": 58239}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-api-stream.png", "path_type": "hardlink", "sha256": "5acb58b2bb3f28e09746ad2973933b991a18ca2f0f289c68a5aaf4ad42cfae8c", "sha256_in_prefix": "5acb58b2bb3f28e09746ad2973933b991a18ca2f0f289c68a5aaf4ad42cfae8c", "size_in_bytes": 49485}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-attach.png", "path_type": "hardlink", "sha256": "1f095e1c470d5f2b426b3090ede30666103db45450ddb44f6877d0cc9078b7c2", "sha256_in_prefix": "1f095e1c470d5f2b426b3090ede30666103db45450ddb44f6877d0cc9078b7c2", "size_in_bytes": 22564}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-connect.png", "path_type": "hardlink", "sha256": "9834a28801522fcac11ae41bf98b81ccbeaa14885a26dd62fa6ad52fbdf2b17a", "sha256_in_prefix": "9834a28801522fcac11ae41bf98b81ccbeaa14885a26dd62fa6ad52fbdf2b17a", "size_in_bytes": 27583}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-connected.png", "path_type": "hardlink", "sha256": "a19b4f5bd36b54aa3203025437decd662fd346450d1b2e42849d2b374654f88b", "sha256_in_prefix": "a19b4f5bd36b54aa3203025437decd662fd346450d1b2e42849d2b374654f88b", "size_in_bytes": 201970}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-interactive-profiling-next-launch.png", "path_type": "hardlink", "sha256": "2503edd3d107a46894365176d7c12fd9f2df92bdd951fce282f3c07d8d4cf7c2", "sha256_in_prefix": "2503edd3d107a46894365176d7c12fd9f2df92bdd951fce282f3c07d8d4cf7c2", "size_in_bytes": 49582}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-profiling-connect.png", "path_type": "hardlink", "sha256": "97ad11b5d931d6a31366b39be1831a04189497495a89813fe4faa4cd970b1169", "sha256_in_prefix": "97ad11b5d931d6a31366b39be1831a04189497495a89813fe4faa4cd970b1169", "size_in_bytes": 149422}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-profiling-options-sections.png", "path_type": "hardlink", "sha256": "1e55a5c49b57bf06a7190ce7cb6c956267068a85dbd40c11d2670a29e0da7b2a", "sha256_in_prefix": "1e55a5c49b57bf06a7190ce7cb6c956267068a85dbd40c11d2670a29e0da7b2a", "size_in_bytes": 70678}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-report.png", "path_type": "hardlink", "sha256": "0cb17aab10f2b977720cf7abaf8976f6469f30d0d412f4ab5c9b2799fa97cf60", "sha256_in_prefix": "0cb17aab10f2b977720cf7abaf8976f6469f30d0d412f4ab5c9b2799fa97cf60", "size_in_bytes": 102285}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-rule.png", "path_type": "hardlink", "sha256": "b6d2a506f0dac18f2f6dd989e0ce0a10935b0e842f275d409d41050189e7fe40", "sha256_in_prefix": "b6d2a506f0dac18f2f6dd989e0ce0a10935b0e842f275d409d41050189e7fe40", "size_in_bytes": 12126}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-system-trace-connect.png", "path_type": "hardlink", "sha256": "36f2469c5b80075efe3a20a9892aa1a2ba43025ded56de55515b8764a4ef9e40", "sha256_in_prefix": "36f2469c5b80075efe3a20a9892aa1a2ba43025ded56de55515b8764a4ef9e40", "size_in_bytes": 53210}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-system-trace-options.png", "path_type": "hardlink", "sha256": "997bd193b4715246121b6cd5706fbd1747e7659ebdf0f150e055786b852c84ea", "sha256_in_prefix": "997bd193b4715246121b6cd5706fbd1747e7659ebdf0f150e055786b852c84ea", "size_in_bytes": 15044}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/quick-start-system-trace-timeline.png", "path_type": "hardlink", "sha256": "b8a817f4ad950f4d7e79881062b5a6988baa22eba69944c3fcb396dc2094a254", "sha256_in_prefix": "b8a817f4ad950f4d7e79881062b5a6988baa22eba69944c3fcb396dc2094a254", "size_in_bytes": 122411}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/regular-application-process.png", "path_type": "hardlink", "sha256": "c074d663f8b7c5cac3f1dd6088863e1421b880e375e24c2d0c0c0c4fe0f66129", "sha256_in_prefix": "c074d663f8b7c5cac3f1dd6088863e1421b880e375e24c2d0c0c0c4fe0f66129", "size_in_bytes": 21702}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/replay-application-kernel-matching.png", "path_type": "hardlink", "sha256": "d30746caa9cf536bfd7f3a067724ba55fd90eb5d5e5bafbb0375b1bc6e85f9ba", "sha256_in_prefix": "d30746caa9cf536bfd7f3a067724ba55fd90eb5d5e5bafbb0375b1bc6e85f9ba", "size_in_bytes": 61693}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/replay-application-range.png", "path_type": "hardlink", "sha256": "55d223b07a7e922d6ef61caae2fed5e15265e33066a8f32cd35b352e27b8c6b9", "sha256_in_prefix": "55d223b07a7e922d6ef61caae2fed5e15265e33066a8f32cd35b352e27b8c6b9", "size_in_bytes": 31910}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/replay-application.png", "path_type": "hardlink", "sha256": "1418699fee530cefd74c5807ad2ec560ca1b75079c5b8b747c335bb525f2a689", "sha256_in_prefix": "1418699fee530cefd74c5807ad2ec560ca1b75079c5b8b747c335bb525f2a689", "size_in_bytes": 16149}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/replay-kernel.png", "path_type": "hardlink", "sha256": "861acd575c0f5aef1bd112121d66bd587a0be9eb6997bc8dfcc4e5e707337c85", "sha256_in_prefix": "861acd575c0f5aef1bd112121d66bd587a0be9eb6997bc8dfcc4e5e707337c85", "size_in_bytes": 19775}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/replay-range.png", "path_type": "hardlink", "sha256": "6e2c678a6eb14901dcb0168b720413607a8f51088f73e4753cf618d756ae08da", "sha256_in_prefix": "6e2c678a6eb14901dcb0168b720413607a8f51088f73e4753cf618d756ae08da", "size_in_bytes": 29920}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/replay-regular-execution.png", "path_type": "hardlink", "sha256": "c7c9eb245d283979063dc0f9a3868601db829cba8e0bf536db7d7b4cdfd01941", "sha256_in_prefix": "c7c9eb245d283979063dc0f9a3868601db829cba8e0bf536db7d7b4cdfd01941", "size_in_bytes": 7029}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/roofline-analysis.png", "path_type": "hardlink", "sha256": "b440bd2fb7a99391c9de23ecbeb2c39e7f4aa5676cc93099a5d5a9de1b453c9c", "sha256_in_prefix": "b440bd2fb7a99391c9de23ecbeb2c39e7f4aa5676cc93099a5d5a9de1b453c9c", "size_in_bytes": 54809}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/roofline-overview.png", "path_type": "hardlink", "sha256": "35761d8c75bb3e5a10979b104c41822f6ee9c61f7f0824323d95c0cafb7c31f4", "sha256_in_prefix": "35761d8c75bb3e5a10979b104c41822f6ee9c61f7f0824323d95c0cafb7c31f4", "size_in_bytes": 59615}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/section-files-2.png", "path_type": "hardlink", "sha256": "d80421b754fa1cf4ca2589ca31c61ad6ef03cf8b65a13374c4d35abc5c7f9034", "sha256_in_prefix": "d80421b754fa1cf4ca2589ca31c61ad6ef03cf8b65a13374c4d35abc5c7f9034", "size_in_bytes": 51327}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/section-files.png", "path_type": "hardlink", "sha256": "2bba5a430958e880dea9cc78833af832b96d781a7812a39dc2115487f15fa5ab", "sha256_in_prefix": "2bba5a430958e880dea9cc78833af832b96d781a7812a39dc2115487f15fa5ab", "size_in_bytes": 2933}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/sm-selection-dialog.png", "path_type": "hardlink", "sha256": "2dbdcc46c8feea3bbfbd4a40a96f495c9aa01ca846f6d654449d34b0d1e3c68c", "sha256_in_prefix": "2dbdcc46c8feea3bbfbd4a40a96f495c9aa01ca846f6d654449d34b0d1e3c68c", "size_in_bytes": 4083}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/source-comparison-document.png", "path_type": "hardlink", "sha256": "94a8111e6abc2c376bde249e89fd76523a326f4d18260b638452c842611d898f", "sha256_in_prefix": "94a8111e6abc2c376bde249e89fd76523a326f4d18260b638452c842611d898f", "size_in_bytes": 192518}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/source-comparison-from-header.png", "path_type": "hardlink", "sha256": "2f7705869c9e8ad6f2a6d4a205844d72aae09c134032dc0c9481f1e801f6db9a", "sha256_in_prefix": "2f7705869c9e8ad6f2a6d4a205844d72aae09c134032dc0c9481f1e801f6db9a", "size_in_bytes": 58994}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/source-comparison-navigation-buttons.png", "path_type": "hardlink", "sha256": "41f752bd3637635edb9b326a16c389f10fc6c062a0c1e6a43690b685bd494f3f", "sha256_in_prefix": "41f752bd3637635edb9b326a16c389f10fc6c062a0c1e6a43690b685bd494f3f", "size_in_bytes": 311015}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/source-counters.png", "path_type": "hardlink", "sha256": "823ebe46de95d3a757e8c21140dd8723a18afaa6eba0e0db90f45a6a5eed3f12", "sha256_in_prefix": "823ebe46de95d3a757e8c21140dd8723a18afaa6eba0e0db90f45a6a5eed3f12", "size_in_bytes": 71869}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/status-banner.png", "path_type": "hardlink", "sha256": "ac8d5f3e6ff8c0e6f85054d36a8f22ff01ffbb089c63959345aaf8673e9a94ed", "sha256_in_prefix": "ac8d5f3e6ff8c0e6f85054d36a8f22ff01ffbb089c63959345aaf8673e9a94ed", "size_in_bytes": 7514}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-api-statistics.png", "path_type": "hardlink", "sha256": "975d1218511bb68ebe820d618278c0210b721c4c0d1f85b096996e2ed3d811de", "sha256_in_prefix": "975d1218511bb68ebe820d618278c0210b721c4c0d1f85b096996e2ed3d811de", "size_in_bytes": 26203}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-api-stream.png", "path_type": "hardlink", "sha256": "223b1a49890cb052fdadff3867094e81fb8e53e665644028d7067ab848821542", "sha256_in_prefix": "223b1a49890cb052fdadff3867094e81fb8e53e665644028d7067ab848821542", "size_in_bytes": 49637}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-baselines.png", "path_type": "hardlink", "sha256": "815ca8b6cab7e6b2c8dcd90dc8713d9be0587f05c3ece9143c76b1394065ebc5", "sha256_in_prefix": "815ca8b6cab7e6b2c8dcd90dc8713d9be0587f05c3ece9143c76b1394065ebc5", "size_in_bytes": 30112}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-launch-details.png", "path_type": "hardlink", "sha256": "7551dbe5ce4551c92ed5474e2f1b7955946722865d4d4df17742ef160c9f421b", "sha256_in_prefix": "7551dbe5ce4551c92ed5474e2f1b7955946722865d4d4df17742ef160c9f421b", "size_in_bytes": 63881}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-metric-details.png", "path_type": "hardlink", "sha256": "05a452a28a70f4ddd6e92f03cebd0f584a6175ee97890b887e5d1008c41bad7b", "sha256_in_prefix": "05a452a28a70f4ddd6e92f03cebd0f584a6175ee97890b887e5d1008c41bad7b", "size_in_bytes": 37809}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-nvtx-resources.png", "path_type": "hardlink", "sha256": "5a81680fd720ebe0efc6073854253bae0f6dcc46150345b06755106373ba6245", "sha256_in_prefix": "5a81680fd720ebe0efc6073854253bae0f6dcc46150345b06755106373ba6245", "size_in_bytes": 7467}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-nvtx.png", "path_type": "hardlink", "sha256": "8e1d714f01e95069dcf86674af69226d350beaae82c7d4b48aaf67230c2fdf03", "sha256_in_prefix": "8e1d714f01e95069dcf86674af69226d350beaae82c7d4b48aaf67230c2fdf03", "size_in_bytes": 8102}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-resources.png", "path_type": "hardlink", "sha256": "fef2c61e7061a0cfb656fc31a08b2fe4069813d3170244d5ec7e44a1780564bb", "sha256_in_prefix": "fef2c61e7061a0cfb656fc31a08b2fe4069813d3170244d5ec7e44a1780564bb", "size_in_bytes": 10710}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-section-sets.png", "path_type": "hardlink", "sha256": "bbc68df06a5b7b92cc67b11a19dab008bae25fd93ee02e590821a17a9d7ebdd0", "sha256_in_prefix": "bbc68df06a5b7b92cc67b11a19dab008bae25fd93ee02e590821a17a9d7ebdd0", "size_in_bytes": 100144}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/tool-window-sections.png", "path_type": "hardlink", "sha256": "52148bb86cab077448fae678823f10b026841cf3aaebdde6ef8e1b93b2f1a8ae", "sha256_in_prefix": "52148bb86cab077448fae678823f10b026841cf3aaebdde6ef8e1b93b2f1a8ae", "size_in_bytes": 293806}, {"_path": "Library/nsight-compute/2024.1.1/docs/_images/welcome-page.png", "path_type": "hardlink", "sha256": "441b033e4d255ad39851b3407c33c529f2caaf53a2d05009ef5def43bdb39510", "sha256_in_prefix": "441b033e4d255ad39851b3407c33c529f2caaf53a2d05009ef5def43bdb39510", "size_in_bytes": 64417}, {"_path": "Library/nsight-compute/2024.1.1/docs/_sphinx_design_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "path_type": "hardlink", "sha256": "35ef98809846a30b25ae6f30881f451a1d4ae9fabdc409eed05d502b724d33c6", "sha256_in_prefix": "35ef98809846a30b25ae6f30881f451a1d4ae9fabdc409eed05d502b724d33c6", "size_in_bytes": 48386}, {"_path": "Library/nsight-compute/2024.1.1/docs/_sphinx_design_static/design-tabs.js", "path_type": "hardlink", "sha256": "00a9989c6d54254f32a37e50c7bbc5dab737674231aa2553421f4939624b23e3", "sha256_in_prefix": "00a9989c6d54254f32a37e50c7bbc5dab737674231aa2553421f4939624b23e3", "size_in_bytes": 770}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/NVIDIA-LogoBlack.svg", "path_type": "hardlink", "sha256": "2b3902d0845adc134d656d464864abd3802a3e09bace5fc70a3c146894648c2a", "sha256_in_prefix": "2b3902d0845adc134d656d464864abd3802a3e09bace5fc70a3c146894648c2a", "size_in_bytes": 2494}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/NVIDIA-LogoWhite.svg", "path_type": "hardlink", "sha256": "ef126c03f44ba0184e158ddc9a177e8adb3f3c175ca80aea3be30d3ca8eb1126", "sha256_in_prefix": "ef126c03f44ba0184e158ddc9a177e8adb3f3c175ca80aea3be30d3ca8eb1126", "size_in_bytes": 3722}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/api-styles-dark.css", "path_type": "hardlink", "sha256": "cdbb2149e908ca91a86f3c9c17bac9b94e96945f3c6f824b69256742a086c5d7", "sha256_in_prefix": "cdbb2149e908ca91a86f3c9c17bac9b94e96945f3c6f824b69256742a086c5d7", "size_in_bytes": 1449}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/api-styles.css", "path_type": "hardlink", "sha256": "df38c2631c3d9de6cc0a2a336f27ee6c13d67f8bfb1cb98267db3cb94c700645", "sha256_in_prefix": "df38c2631c3d9de6cc0a2a336f27ee6c13d67f8bfb1cb98267db3cb94c700645", "size_in_bytes": 2436}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/basic.css", "path_type": "hardlink", "sha256": "55e3151630469fe605cc1730536cc11c0f3c5685796f4ea1392c24709b094386", "sha256_in_prefix": "55e3151630469fe605cc1730536cc11c0f3c5685796f4ea1392c24709b094386", "size_in_bytes": 14692}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/badge_only.css", "path_type": "hardlink", "sha256": "c4050fa47d8bb6297c79811f663e6cfa32cb6b783b47eaeddd6ba50d5cf1a666", "sha256_in_prefix": "c4050fa47d8bb6297c79811f663e6cfa32cb6b783b47eaeddd6ba50d5cf1a666", "size_in_bytes": 3275}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/Roboto-Slab-Bold.woff", "path_type": "hardlink", "sha256": "9fec87cadbe2413b255f1ec577573a83f1ca2e1c37aa023dbebcd3a7b864636a", "sha256_in_prefix": "9fec87cadbe2413b255f1ec577573a83f1ca2e1c37aa023dbebcd3a7b864636a", "size_in_bytes": 87624}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/Roboto-Slab-Bold.woff2", "path_type": "hardlink", "sha256": "1a0c024dd1a267c52d5575469ffe8570d1e84164de7d393cf3414bafd17d7a0c", "sha256_in_prefix": "1a0c024dd1a267c52d5575469ffe8570d1e84164de7d393cf3414bafd17d7a0c", "size_in_bytes": 67312}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/Roboto-Slab-Regular.woff", "path_type": "hardlink", "sha256": "9f32630e2c0c5135bf1e86e36cb65b3932e4410644235bc2bd995e9c7f6ff117", "sha256_in_prefix": "9f32630e2c0c5135bf1e86e36cb65b3932e4410644235bc2bd995e9c7f6ff117", "size_in_bytes": 86288}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/Roboto-Slab-Regular.woff2", "path_type": "hardlink", "sha256": "874e42222856d7af03b3f438d21d923a4280d47fe67c48510e2174a1579795ef", "sha256_in_prefix": "874e42222856d7af03b3f438d21d923a4280d47fe67c48510e2174a1579795ef", "size_in_bytes": 66444}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.eot", "path_type": "hardlink", "sha256": "7bfcab6db99d5cfbf1705ca0536ddc78585432cc5fa41bbd7ad0f009033b2979", "sha256_in_prefix": "7bfcab6db99d5cfbf1705ca0536ddc78585432cc5fa41bbd7ad0f009033b2979", "size_in_bytes": 165742}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.svg", "path_type": "hardlink", "sha256": "ad6157926c1622ba4e1d03d478f1541368524bfc46f51e42fe0d945f7ef323e4", "sha256_in_prefix": "ad6157926c1622ba4e1d03d478f1541368524bfc46f51e42fe0d945f7ef323e4", "size_in_bytes": 444379}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.ttf", "path_type": "hardlink", "sha256": "aa58f33f239a0fb02f5c7a6c45c043d7a9ac9a093335806694ecd6d4edc0d6a8", "sha256_in_prefix": "aa58f33f239a0fb02f5c7a6c45c043d7a9ac9a093335806694ecd6d4edc0d6a8", "size_in_bytes": 165548}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.woff", "path_type": "hardlink", "sha256": "ba0c59deb5450f5cb41b3f93609ee2d0d995415877ddfa223e8a8a7533474f07", "sha256_in_prefix": "ba0c59deb5450f5cb41b3f93609ee2d0d995415877ddfa223e8a8a7533474f07", "size_in_bytes": 98024}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/fontawesome-webfont.woff2", "path_type": "hardlink", "sha256": "2adefcbc041e7d18fcf2d417879dc5a09997aa64d675b7a3c4b6ce33da13f3fe", "sha256_in_prefix": "2adefcbc041e7d18fcf2d417879dc5a09997aa64d675b7a3c4b6ce33da13f3fe", "size_in_bytes": 77160}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-bold-italic.woff", "path_type": "hardlink", "sha256": "980c8592e5488df256192c999e92db8fd302db8cd8909b7fa266a684e37e45f8", "sha256_in_prefix": "980c8592e5488df256192c999e92db8fd302db8cd8909b7fa266a684e37e45f8", "size_in_bytes": 323344}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-bold-italic.woff2", "path_type": "hardlink", "sha256": "c0916a33340d063f7b05679e08031e729d1888444706f04804705da5966d895d", "sha256_in_prefix": "c0916a33340d063f7b05679e08031e729d1888444706f04804705da5966d895d", "size_in_bytes": 193308}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-bold.woff", "path_type": "hardlink", "sha256": "0e56b17d142eb366c8007031d14e34da48c70b4a9d9a0ca492e696a7bae45e1e", "sha256_in_prefix": "0e56b17d142eb366c8007031d14e34da48c70b4a9d9a0ca492e696a7bae45e1e", "size_in_bytes": 309728}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-bold.woff2", "path_type": "hardlink", "sha256": "ae88fc0d7a961832f809527d30bd3983a6866d42f66a56ade23f543681594db6", "sha256_in_prefix": "ae88fc0d7a961832f809527d30bd3983a6866d42f66a56ade23f543681594db6", "size_in_bytes": 184912}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-normal-italic.woff", "path_type": "hardlink", "sha256": "26318a1467a5e5caf10b04cfa942d079632560cd7a29cec565fd1dc9f7ec5081", "sha256_in_prefix": "26318a1467a5e5caf10b04cfa942d079632560cd7a29cec565fd1dc9f7ec5081", "size_in_bytes": 328412}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-normal-italic.woff2", "path_type": "hardlink", "sha256": "4465765f2f6eddcdad34ffd7cab559e56bc0e75e45e192f85e9562b0771481dc", "sha256_in_prefix": "4465765f2f6eddcdad34ffd7cab559e56bc0e75e45e192f85e9562b0771481dc", "size_in_bytes": 195704}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-normal.woff", "path_type": "hardlink", "sha256": "5b9025dda4d7688e3311b0c17eddc501133b807def33effaef6593843cf5416e", "sha256_in_prefix": "5b9025dda4d7688e3311b0c17eddc501133b807def33effaef6593843cf5416e", "size_in_bytes": 309192}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/fonts/lato-normal.woff2", "path_type": "hardlink", "sha256": "983b0caf336e8542214fc17019a4fc5e0360864b92806ca14d55c1fc1c2c5a0f", "sha256_in_prefix": "983b0caf336e8542214fc17019a4fc5e0360864b92806ca14d55c1fc1c2c5a0f", "size_in_bytes": 182708}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/css/theme.css", "path_type": "hardlink", "sha256": "af78284d3c7a8ab0917208ddc2a71854d4b552802104a7146f8705dca8dd88d1", "sha256_in_prefix": "af78284d3c7a8ab0917208ddc2a71854d4b552802104a7146f8705dca8dd88d1", "size_in_bytes": 129674}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/design-style.b7bb847fb20b106c3d81b95245e65545.min.css", "path_type": "hardlink", "sha256": "35ef98809846a30b25ae6f30881f451a1d4ae9fabdc409eed05d502b724d33c6", "sha256_in_prefix": "35ef98809846a30b25ae6f30881f451a1d4ae9fabdc409eed05d502b724d33c6", "size_in_bytes": 48386}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/design-tabs.js", "path_type": "hardlink", "sha256": "00a9989c6d54254f32a37e50c7bbc5dab737674231aa2553421f4939624b23e3", "sha256_in_prefix": "00a9989c6d54254f32a37e50c7bbc5dab737674231aa2553421f4939624b23e3", "size_in_bytes": 770}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/doctools.js", "path_type": "hardlink", "sha256": "b5cad4208b5895e6182a3d6ba2a28c38ba4c3ed7ddff4635839aa430eee59614", "sha256_in_prefix": "b5cad4208b5895e6182a3d6ba2a28c38ba4c3ed7ddff4635839aa430eee59614", "size_in_bytes": 10766}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/documentation_options.js", "path_type": "hardlink", "sha256": "98060326ec52c44b7d376108726101f002ae7d9019322f06c96f711e0201c812", "sha256_in_prefix": "98060326ec52c44b7d376108726101f002ae7d9019322f06c96f711e0201c812", "size_in_bytes": 422}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/favicon.ico", "path_type": "hardlink", "sha256": "2e4c2d0a267ad715477ab9b3ba55358d6af5afbdee9198ffd76111bb08972575", "sha256_in_prefix": "2e4c2d0a267ad715477ab9b3ba55358d6af5afbdee9198ffd76111bb08972575", "size_in_bytes": 15406}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/file.png", "path_type": "hardlink", "sha256": "5c4bc9a16aebf38c4b950f59b8e501ca36495328cb9eb622218bce9064a35e3e", "sha256_in_prefix": "5c4bc9a16aebf38c4b950f59b8e501ca36495328cb9eb622218bce9064a35e3e", "size_in_bytes": 286}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/jquery-3.5.1.js", "path_type": "hardlink", "sha256": "416a3b2c3bf16d64f6b5b6d0f7b079df2267614dd6847fc2f3271b4409233c37", "sha256_in_prefix": "416a3b2c3bf16d64f6b5b6d0f7b079df2267614dd6847fc2f3271b4409233c37", "size_in_bytes": 287630}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/jquery.js", "path_type": "hardlink", "sha256": "f7f6a5894f1d19ddad6fa392b2ece2c5e578cbf7da4ea805b6885eb6985b6e3d", "sha256_in_prefix": "f7f6a5894f1d19ddad6fa392b2ece2c5e578cbf7da4ea805b6885eb6985b6e3d", "size_in_bytes": 89476}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/js/badge_only.js", "path_type": "hardlink", "sha256": "f0a4808d04c4d55378751ac096a8376b64b1a704c82584b0ee590212cf413013", "sha256_in_prefix": "f0a4808d04c4d55378751ac096a8376b64b1a704c82584b0ee590212cf413013", "size_in_bytes": 934}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/js/html5shiv-printshiv.min.js", "path_type": "hardlink", "sha256": "b42a7e949a6e21d66b30fbbb4a22deafd9e0ccabc04f0fa2907fc6252fdf165f", "sha256_in_prefix": "b42a7e949a6e21d66b30fbbb4a22deafd9e0ccabc04f0fa2907fc6252fdf165f", "size_in_bytes": 4370}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/js/html5shiv.min.js", "path_type": "hardlink", "sha256": "f6e0283561ddb33b140e14977ffad57163aa28f7e2e7ff15e51e1475b6657b60", "sha256_in_prefix": "f6e0283561ddb33b140e14977ffad57163aa28f7e2e7ff15e51e1475b6657b60", "size_in_bytes": 2734}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/js/theme.js", "path_type": "hardlink", "sha256": "536ad2d746e944c5570cc15badaeccc3c0582a1b66e45511fe4edce32b6da510", "sha256_in_prefix": "536ad2d746e944c5570cc15badaeccc3c0582a1b66e45511fe4edce32b6da510", "size_in_bytes": 5023}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/language_data.js", "path_type": "hardlink", "sha256": "661af9e0ff14a7031d97977920de438313c4649f7fa9998702c9dba550dd4f59", "sha256_in_prefix": "661af9e0ff14a7031d97977920de438313c4649f7fa9998702c9dba550dd4f59", "size_in_bytes": 325}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/lunr.min.js", "path_type": "hardlink", "sha256": "0c50d9002b85780a842afffb567bb54ede402dae7c6dc5997a018614d8044fc8", "sha256_in_prefix": "0c50d9002b85780a842afffb567bb54ede402dae7c6dc5997a018614d8044fc8", "size_in_bytes": 29510}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/lunr_search.js", "path_type": "hardlink", "sha256": "c085b405731428d93654b0bea8cad425080fcfd6c6355445632bcaafa559d0da", "sha256_in_prefix": "c085b405731428d93654b0bea8cad425080fcfd6c6355445632bcaafa559d0da", "size_in_bytes": 5417}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/main_ov_logo_rect.png", "path_type": "hardlink", "sha256": "b15c15d30ac536c3fc4401c8d10abb699dc5b421d68ef6b394dfb4c617933dda", "sha256_in_prefix": "b15c15d30ac536c3fc4401c8d10abb699dc5b421d68ef6b394dfb4c617933dda", "size_in_bytes": 57423}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/main_ov_logo_square.png", "path_type": "hardlink", "sha256": "06b7ba35792a640bf5725c179b84750d5ab670a88ccc94a8b4360997c08c19b8", "sha256_in_prefix": "06b7ba35792a640bf5725c179b84750d5ab670a88ccc94a8b4360997c08c19b8", "size_in_bytes": 195331}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/mermaid-init.js", "path_type": "hardlink", "sha256": "965280720746f4b5e8053dd059e114dacf87afe6ee4ff4214f2dbf9fb472f75c", "sha256_in_prefix": "965280720746f4b5e8053dd059e114dacf87afe6ee4ff4214f2dbf9fb472f75c", "size_in_bytes": 1293}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/minus.png", "path_type": "hardlink", "sha256": "47e7fc50db3699f1ca41ce9a2ffa202c00c5d1d5180c55f62ba859b1bd6cc008", "sha256_in_prefix": "47e7fc50db3699f1ca41ce9a2ffa202c00c5d1d5180c55f62ba859b1bd6cc008", "size_in_bytes": 90}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/nsight-compute.ico", "path_type": "hardlink", "sha256": "f92fccc470e532adf31761d920ffa23da4a3dc52058d8f984157d069bd620e37", "sha256_in_prefix": "f92fccc470e532adf31761d920ffa23da4a3dc52058d8f984157d069bd620e37", "size_in_bytes": 256267}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/nsight-compute.png", "path_type": "hardlink", "sha256": "de827e5c16259c7a271db1058d626e5ae18cd39c3cf55a00c77b1b9e9d8cd28e", "sha256_in_prefix": "de827e5c16259c7a271db1058d626e5ae18cd39c3cf55a00c77b1b9e9d8cd28e", "size_in_bytes": 42166}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/omni-style-dark.css", "path_type": "hardlink", "sha256": "cfff2820f17dd2f0eefdc436a0ad1c8f568d0e4a7b9c4b4ab01e8e084a4f7ae4", "sha256_in_prefix": "cfff2820f17dd2f0eefdc436a0ad1c8f568d0e4a7b9c4b4ab01e8e084a4f7ae4", "size_in_bytes": 13807}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/omni-style.css", "path_type": "hardlink", "sha256": "fc85701c093f8282be71da5867fc7d41a706a8c2aa928986e24d995ef27ad430", "sha256_in_prefix": "fc85701c093f8282be71da5867fc7d41a706a8c2aa928986e24d995ef27ad430", "size_in_bytes": 46642}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/plus.png", "path_type": "hardlink", "sha256": "54115199b96a130cba02147c47c0deb43dcc9b9f08b5162bba8642b34980ac63", "sha256_in_prefix": "54115199b96a130cba02147c47c0deb43dcc9b9f08b5162bba8642b34980ac63", "size_in_bytes": 90}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/pygments.css", "path_type": "hardlink", "sha256": "f82f422053b4413684181f281e3cfcc2e84bea525d66feb8116f9dbe8674fcc2", "sha256_in_prefix": "f82f422053b4413684181f281e3cfcc2e84bea525d66feb8116f9dbe8674fcc2", "size_in_bytes": 4819}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/searchtools.js", "path_type": "hardlink", "sha256": "d6b5ee21edd7b46c029c5111326719dcec5c5f52368704a93b2d6485cb22414c", "sha256_in_prefix": "d6b5ee21edd7b46c029c5111326719dcec5c5f52368704a93b2d6485cb22414c", "size_in_bytes": 16634}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/social-media.js", "path_type": "hardlink", "sha256": "85753a680a9315ff225203827b5c43d4d5effa8c7e3c571e5388dd73746ca512", "sha256_in_prefix": "85753a680a9315ff225203827b5c43d4d5effa8c7e3c571e5388dd73746ca512", "size_in_bytes": 2393}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/theme-setter.js", "path_type": "hardlink", "sha256": "d6feb784f694a13844bef039ad96fe3854ba23cea8f85f6313733af104d6332a", "sha256_in_prefix": "d6feb784f694a13844bef039ad96fe3854ba23cea8f85f6313733af104d6332a", "size_in_bytes": 1713}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/theme-switcher-general.css", "path_type": "hardlink", "sha256": "cfaf6ac2d70d331348a3f0652ab7b25a3689c003c040e4b1ec90be2702dc2c3a", "sha256_in_prefix": "cfaf6ac2d70d331348a3f0652ab7b25a3689c003c040e4b1ec90be2702dc2c3a", "size_in_bytes": 900}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/twemoji.css", "path_type": "hardlink", "sha256": "79f024cf4b763993639a7db8d2951b43e6a61619a50bc4fa92e3d3e20ccf8363", "sha256_in_prefix": "79f024cf4b763993639a7db8d2951b43e6a61619a50bc4fa92e3d3e20ccf8363", "size_in_bytes": 103}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/twemoji.js", "path_type": "hardlink", "sha256": "4c98db8e2416f696e9805f90294c56fae45c71f1cae843c25d69c28087ad691b", "sha256_in_prefix": "4c98db8e2416f696e9805f90294c56fae45c71f1cae843c25d69c28087ad691b", "size_in_bytes": 332}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/underscore-1.13.1.js", "path_type": "hardlink", "sha256": "cc10f799cd0f6b65f95c4012445497e5ba3cb9f51964a9468940b27bde98b487", "sha256_in_prefix": "cc10f799cd0f6b65f95c4012445497e5ba3cb9f51964a9468940b27bde98b487", "size_in_bytes": 68420}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/underscore.js", "path_type": "hardlink", "sha256": "218fb1c1fc72e9af6b866f430be2a67fa376392b4db2f4dbf32772671b6ae55c", "sha256_in_prefix": "218fb1c1fc72e9af6b866f430be2a67fa376392b4db2f4dbf32772671b6ae55c", "size_in_bytes": 19530}, {"_path": "Library/nsight-compute/2024.1.1/docs/_static/version.js", "path_type": "hardlink", "sha256": "55a78cbd5a8c9d2b422d3e19dd624e48625f76146d5b7fc873261033f0fda528", "sha256_in_prefix": "55a78cbd5a8c9d2b422d3e19dd624e48625f76146d5b7fc873261033f0fda528", "size_in_bytes": 6294}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IAction.html", "path_type": "hardlink", "sha256": "2fb66bfb3d136740b57f6aaaec4762e1917b3964c4247e1fa793baf37df2715a", "sha256_in_prefix": "2fb66bfb3d136740b57f6aaaec4762e1917b3964c4247e1fa793baf37df2715a", "size_in_bytes": 39395}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IBaseContext.html", "path_type": "hardlink", "sha256": "b66578c1c15ced6b5dd7d62848e43377d569d758776e8e35795e9a504ffc29da", "sha256_in_prefix": "b66578c1c15ced6b5dd7d62848e43377d569d758776e8e35795e9a504ffc29da", "size_in_bytes": 12833}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IContext.html", "path_type": "hardlink", "sha256": "76ff49652dead1cf9eecf5b1197d1cd2e83076d7b1ce967f4ad3f3460ba4b5e3", "sha256_in_prefix": "76ff49652dead1cf9eecf5b1197d1cd2e83076d7b1ce967f4ad3f3460ba4b5e3", "size_in_bytes": 18478}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IController.html", "path_type": "hardlink", "sha256": "9cf48466820f743745d6334112aa4ac2a14a3717c926a95cdd086a3b5a947147", "sha256_in_prefix": "9cf48466820f743745d6334112aa4ac2a14a3717c926a95cdd086a3b5a947147", "size_in_bytes": 13099}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IEvaluator.html", "path_type": "hardlink", "sha256": "1860175a4b4df35752601dc8830b99ac4651f4011db72ab44b0c4550b34480c9", "sha256_in_prefix": "1860175a4b4df35752601dc8830b99ac4651f4011db72ab44b0c4550b34480c9", "size_in_bytes": 16662}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IFrontend.html", "path_type": "hardlink", "sha256": "8c73c03f1d119092dc36b7b81e6549c681534f2bcf89a82069be9feb7ba1250f", "sha256_in_prefix": "8c73c03f1d119092dc36b7b81e6549c681534f2bcf89a82069be9feb7ba1250f", "size_in_bytes": 48142}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IMessageVault.html", "path_type": "hardlink", "sha256": "da9a2b90caed554bf14aa8b014b8bfbaa8b0427a9a74e144659209ff24ad4269", "sha256_in_prefix": "da9a2b90caed554bf14aa8b014b8bfbaa8b0427a9a74e144659209ff24ad4269", "size_in_bytes": 16782}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IMetric.html", "path_type": "hardlink", "sha256": "0d7f10c9da11d5ccaeee844051831dcfddb64df38544f27ce565d1f6f5d30e9e", "sha256_in_prefix": "0d7f10c9da11d5ccaeee844051831dcfddb64df38544f27ce565d1f6f5d30e9e", "size_in_bytes": 68285}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1INvtxDomainInfo.html", "path_type": "hardlink", "sha256": "a104a7698dc5dd8434f48c92af746f4bf8c86fcd740cf81c6f11630cab17209e", "sha256_in_prefix": "a104a7698dc5dd8434f48c92af746f4bf8c86fcd740cf81c6f11630cab17209e", "size_in_bytes": 17567}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1INvtxRange.html", "path_type": "hardlink", "sha256": "af84cd2324455fd079198468e23f95a3783ad14d2622505603151baf9c3791a3", "sha256_in_prefix": "af84cd2324455fd079198468e23f95a3783ad14d2622505603151baf9c3791a3", "size_in_bytes": 28997}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1INvtxState.html", "path_type": "hardlink", "sha256": "bb201f80230bc37bb757c800f7acdf42db9b84609f28ad63e0d52e4fd3ee33fe", "sha256_in_prefix": "bb201f80230bc37bb757c800f7acdf42db9b84609f28ad63e0d52e4fd3ee33fe", "size_in_bytes": 13210}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1IRange.html", "path_type": "hardlink", "sha256": "a4f28a4a3407e5079175c0c5b354b682a5d248e8093630546387e476e250c1b7", "sha256_in_prefix": "a4f28a4a3407e5079175c0c5b354b682a5d248e8093630546387e476e250c1b7", "size_in_bytes": 15852}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/classNV_1_1Rules_1_1ISourceInfo.html", "path_type": "hardlink", "sha256": "2ba35f6e77f289811a3ef7c3d9ad168d8a191011c2592a806faad5a4657c0f5a", "sha256_in_prefix": "2ba35f6e77f289811a3ef7c3d9ad168d8a191011c2592a806faad5a4657c0f5a", "size_in_bytes": 12304}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/data-structures.html", "path_type": "hardlink", "sha256": "c3a890e283f15ccc1f2f03de9788360427846870aa46557a2082fec119d42df7", "sha256_in_prefix": "c3a890e283f15ccc1f2f03de9788360427846870aa46557a2082fec119d42df7", "size_in_bytes": 8834}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/group__NVRULES__HW.html", "path_type": "hardlink", "sha256": "f03a48206c254a76ebc6dcfa5db4968a3509dbb1aec95ffb3d5b2dbf54290797", "sha256_in_prefix": "f03a48206c254a76ebc6dcfa5db4968a3509dbb1aec95ffb3d5b2dbf54290797", "size_in_bytes": 8288}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/group__NVRULES__LW.html", "path_type": "hardlink", "sha256": "a70ccd15e5561c22609bf93b50c6dd16e9801ee3eff6ed5ff8c3cd45ee4a62e1", "sha256_in_prefix": "a70ccd15e5561c22609bf93b50c6dd16e9801ee3eff6ed5ff8c3cd45ee4a62e1", "size_in_bytes": 8498}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/group__NVRULES__NM.html", "path_type": "hardlink", "sha256": "3122b4d13a1bb6920cdd4fa6b7e78926ac8b059a83e8f291dd5d1a00e1d14fc1", "sha256_in_prefix": "3122b4d13a1bb6920cdd4fa6b7e78926ac8b059a83e8f291dd5d1a00e1d14fc1", "size_in_bytes": 17340}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/modules.html", "path_type": "hardlink", "sha256": "15adba4c8dad72bb44b360de69b2abf068d8b37930f586c372e28759da162489", "sha256_in_prefix": "15adba4c8dad72bb44b360de69b2abf068d8b37930f586c372e28759da162489", "size_in_bytes": 7414}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/namespaceNV.html", "path_type": "hardlink", "sha256": "a1cefaae0de7a0654cc9a958f5b6cc4c68dbd68aaaf47cb7e50dd51d1b71f926", "sha256_in_prefix": "a1cefaae0de7a0654cc9a958f5b6cc4c68dbd68aaaf47cb7e50dd51d1b71f926", "size_in_bytes": 7245}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/namespaceNV_1_1Rules.html", "path_type": "hardlink", "sha256": "6279a026d77a7ff34109319dd81dc8ab0d4338a1a4833939e99823f03555d3c0", "sha256_in_prefix": "6279a026d77a7ff34109319dd81dc8ab0d4338a1a4833939e99823f03555d3c0", "size_in_bytes": 15282}, {"_path": "Library/nsight-compute/2024.1.1/docs/api/namespaces.html", "path_type": "hardlink", "sha256": "76cd72d766fcec5b005d184d2693d34d5aa3a28f476d38688ae76129c2284cdb", "sha256_in_prefix": "76cd72d766fcec5b005d184d2693d34d5aa3a28f476d38688ae76129c2284cdb", "size_in_bytes": 6652}, {"_path": "Library/nsight-compute/2024.1.1/docs/genindex.html", "path_type": "hardlink", "sha256": "d84a4a5c4cad9781d6b26eb6aa55057cfca2260a0e655cc91df0a9753220ee75", "sha256_in_prefix": "d84a4a5c4cad9781d6b26eb6aa55057cfca2260a0e655cc91df0a9753220ee75", "size_in_bytes": 36557}, {"_path": "Library/nsight-compute/2024.1.1/docs/index.html", "path_type": "hardlink", "sha256": "748a91187427aefd4edf598fba4c5106829ca8aa8ea903cc83460d504d9cd56a", "sha256_in_prefix": "748a91187427aefd4edf598fba4c5106829ca8aa8ea903cc83460d504d9cd56a", "size_in_bytes": 8883}, {"_path": "Library/nsight-compute/2024.1.1/docs/objects.inv", "path_type": "hardlink", "sha256": "8cd4063288312f566ca8cbb4b239ac7f50e72ffd25d9cf4d44ad0e2005e10239", "sha256_in_prefix": "8cd4063288312f566ca8cbb4b239ac7f50e72ffd25d9cf4d44ad0e2005e10239", "size_in_bytes": 11978}, {"_path": "Library/nsight-compute/2024.1.1/docs/project.json", "path_type": "hardlink", "sha256": "a6f3772f34242fa736f2be8b8c09f31a1d82ef987b52a067ccaed513b1010ba0", "sha256_in_prefix": "a6f3772f34242fa736f2be8b8c09f31a1d82ef987b52a067ccaed513b1010ba0", "size_in_bytes": 44}, {"_path": "Library/nsight-compute/2024.1.1/docs/search.html", "path_type": "hardlink", "sha256": "820be3c1b920e5dab2410e99771d4e005fa3a538f82be1bf4e9c9c386415be60", "sha256_in_prefix": "820be3c1b920e5dab2410e99771d4e005fa3a538f82be1bf4e9c9c386415be60", "size_in_bytes": 5542}, {"_path": "Library/nsight-compute/2024.1.1/docs/searchindex.js", "path_type": "hardlink", "sha256": "a201ae29139f20e6a9f39a2e06bda9cd15907fbde09e1e785139d043a8edb6c0", "sha256_in_prefix": "a201ae29139f20e6a9f39a2e06bda9cd15907fbde09e1e785139d043a8edb6c0", "size_in_bytes": 900778}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/CpuStacktrace.proto", "path_type": "hardlink", "sha256": "6cf077c254ace4627aaae14b4d006c2892f6b6adc5bc7bc53ab94b6429c07331", "sha256_in_prefix": "6cf077c254ace4627aaae14b4d006c2892f6b6adc5bc7bc53ab94b6429c07331", "size_in_bytes": 1809}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/Nvtx.proto", "path_type": "hardlink", "sha256": "2d08d6750adc38ef9e5cdfb5789f896fc0bd20b6e7db037591116381f96912fa", "sha256_in_prefix": "2d08d6750adc38ef9e5cdfb5789f896fc0bd20b6e7db037591116381f96912fa", "size_in_bytes": 5103}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/NvtxCategories.proto", "path_type": "hardlink", "sha256": "78d2812b6be8919bb31b9eba66a643fb13284f2d94798f1b6ed15dedaad200f6", "sha256_in_prefix": "78d2812b6be8919bb31b9eba66a643fb13284f2d94798f1b6ed15dedaad200f6", "size_in_bytes": 324}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerMetricOptions.proto", "path_type": "hardlink", "sha256": "c3002cc76adc97db768a765da93f152e5c62688b43d432dfa4251a094f2f6fc2", "sha256_in_prefix": "c3002cc76adc97db768a765da93f152e5c62688b43d432dfa4251a094f2f6fc2", "size_in_bytes": 1554}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerReport.proto", "path_type": "hardlink", "sha256": "7fc98f740fb15c7e98ae1f0e5fdca9fd269457c26cac42f8ef69875308b8ed42", "sha256_in_prefix": "7fc98f740fb15c7e98ae1f0e5fdca9fd269457c26cac42f8ef69875308b8ed42", "size_in_bytes": 17906}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerReportCommon.proto", "path_type": "hardlink", "sha256": "c14fe2b9f3ebeb40cec9bc2fcdaeb8490ea037893759b0e88716f81634e510d1", "sha256_in_prefix": "c14fe2b9f3ebeb40cec9bc2fcdaeb8490ea037893759b0e88716f81634e510d1", "size_in_bytes": 2198}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerResultsCommon.proto", "path_type": "hardlink", "sha256": "d30d71566a5b3a94c71588a85fbb4b40eeb321d72ea2fc6e4745c5c932053016", "sha256_in_prefix": "d30d71566a5b3a94c71588a85fbb4b40eeb321d72ea2fc6e4745c5c932053016", "size_in_bytes": 1021}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerSection.proto", "path_type": "hardlink", "sha256": "4649969972d8de05a107d58318ee77d41045d98bfe51459251680c7653df3ed9", "sha256_in_prefix": "4649969972d8de05a107d58318ee77d41045d98bfe51459251680c7653df3ed9", "size_in_bytes": 24939}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/ProfilerStringTable.proto", "path_type": "hardlink", "sha256": "11cc7b1b517121f06bd9260141d11842c487cac1abc0b68396a7cf6a9df29dbb", "sha256_in_prefix": "11cc7b1b517121f06bd9260141d11842c487cac1abc0b68396a7cf6a9df29dbb", "size_in_bytes": 209}, {"_path": "Library/nsight-compute/2024.1.1/extras/FileFormat/RuleResults.proto", "path_type": "hardlink", "sha256": "e0764d9857300b7df27cbac175dbddec1b52645c0e8576e5e07ff0393fd27442", "sha256_in_prefix": "e0764d9857300b7df27cbac175dbddec1b52645c0e8576e5e07ff0393fd27442", "size_in_bytes": 5275}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/AdvancedRuleTemplate.py", "path_type": "hardlink", "sha256": "c55c0b66113806c43f38e77a1afab0263890f2e110ef417ce1792a98f8c01ffb", "sha256_in_prefix": "c55c0b66113806c43f38e77a1afab0263890f2e110ef417ce1792a98f8c01ffb", "size_in_bytes": 1135}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/BasicKernelInfo.py", "path_type": "hardlink", "sha256": "e8e57080cb91f95ae7738d5a8182e4097aaec71ebc3e1d56335f77f6cb30dde5", "sha256_in_prefix": "e8e57080cb91f95ae7738d5a8182e4097aaec71ebc3e1d56335f77f6cb30dde5", "size_in_bytes": 1213}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/BasicRuleTemplate.py", "path_type": "hardlink", "sha256": "c625d283ccf95ccc886cad1b7524d72f300aa49d5e7a644f75885deded628ddd", "sha256_in_prefix": "c625d283ccf95ccc886cad1b7524d72f300aa49d5e7a644f75885deded628ddd", "size_in_bytes": 1886}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/KernelInstanceBoundsAnalysis.py", "path_type": "hardlink", "sha256": "c2adb2e2d86788820b3e2d7a01094b6138ae81eee657447d2a81e679c3c19660", "sha256_in_prefix": "c2adb2e2d86788820b3e2d7a01094b6138ae81eee657447d2a81e679c3c19660", "size_in_bytes": 3071}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/KernelInstanceBoundsAnalysis.section", "path_type": "hardlink", "sha256": "86e454be56671e186e74490f20efcf3084acb8ffed0246a486272f10a439cd84", "sha256_in_prefix": "86e454be56671e186e74490f20efcf3084acb8ffed0246a486272f10a439cd84", "size_in_bytes": 1008}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/RuleTemplate.section", "path_type": "hardlink", "sha256": "d4dbad7903fcf1949d7dad1b1a58f373a62efb3a893b63e715c526dda7dfd762", "sha256_in_prefix": "d4dbad7903fcf1949d7dad1b1a58f373a62efb3a893b63e715c526dda7dfd762", "size_in_bytes": 432}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/RuleTemplate2_table.chart", "path_type": "hardlink", "sha256": "f54a591bfdb4156ca5ddaba354b68195992eb48120c5795e60da9ab033712605", "sha256_in_prefix": "f54a591bfdb4156ca5ddaba354b68195992eb48120c5795e60da9ab033712605", "size_in_bytes": 212}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/RuleTemplate_bar.chart", "path_type": "hardlink", "sha256": "b93a6b97ac5c1c3404e90ec01f9c73e30ecdd4f4ee8cb82bf05175d9426d3be8", "sha256_in_prefix": "b93a6b97ac5c1c3404e90ec01f9c73e30ecdd4f4ee8cb82bf05175d9426d3be8", "size_in_bytes": 251}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/RuleTemplate_table.chart", "path_type": "hardlink", "sha256": "f2aff5fdea87120877bc9adc6c20adbb040a6abeb5d812781373dc27cdbe3cfb", "sha256_in_prefix": "f2aff5fdea87120877bc9adc6c20adbb040a6abeb5d812781373dc27cdbe3cfb", "size_in_bytes": 227}, {"_path": "Library/nsight-compute/2024.1.1/extras/RuleTemplates/SpeedupWithFocusMetrics.py", "path_type": "hardlink", "sha256": "33faf81ef0e863788e97f74197e8e2ab15e6c5ca6965cb874d52c703737837f2", "sha256_in_prefix": "33faf81ef0e863788e97f74197e8e2ab15e6c5ca6965cb874d52c703737837f2", "size_in_bytes": 2563}, {"_path": "Library/nsight-compute/2024.1.1/extras/python/_ncu_report.pyd", "path_type": "hardlink", "sha256": "116598174e4c99b483f088765c76b9c6bf72153f32e791366472223cb6da4dee", "sha256_in_prefix": "116598174e4c99b483f088765c76b9c6bf72153f32e791366472223cb6da4dee", "size_in_bytes": 42884608}, {"_path": "Library/nsight-compute/2024.1.1/extras/python/ncu_report.py", "path_type": "hardlink", "sha256": "b61f1c5a6c7a5f11457ccbb70c5ac09d5315bf9b67b97d3547c1b5b2db9b930c", "sha256_in_prefix": "b61f1c5a6c7a5f11457ccbb70c5ac09d5315bf9b67b97d3547c1b5b2db9b930c", "size_in_bytes": 106017}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/README.TXT", "path_type": "hardlink", "sha256": "93e1ab29b94200d4b92a0c3eef3b705b935fa0e2563a2bd64632e3fda114523c", "sha256_in_prefix": "93e1ab29b94200d4b92a0c3eef3b705b935fa0e2563a2bd64632e3fda114523c", "size_in_bytes": 2433}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/instructionMix.cu", "path_type": "hardlink", "sha256": "956d9f32899ebc6ba336651671e38d740546e996a5ba8e09af28365bc973b79c", "sha256_in_prefix": "956d9f32899ebc6ba336651671e38d740546e996a5ba8e09af28365bc973b79c", "size_in_bytes": 7737}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/instructionMix.pdf", "path_type": "hardlink", "sha256": "ec6466eb83b2ec91f643db7f1365c4d4556a5fef083ae05b678a44d1555c5948", "sha256_in_prefix": "ec6466eb83b2ec91f643db7f1365c4d4556a5fef083ae05b678a44d1555c5948", "size_in_bytes": 3590961}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/sobelDouble.ncu-rep", "path_type": "hardlink", "sha256": "7d68fa9592424291373dff04d8aee1f321dec056981574a2d475d01fbe3723d1", "sha256_in_prefix": "7d68fa9592424291373dff04d8aee1f321dec056981574a2d475d01fbe3723d1", "size_in_bytes": 666104}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/instructionMix/sobelFloat.ncu-rep", "path_type": "hardlink", "sha256": "e9f06ae605bc54157c55c5d48a9f3981870eb44ab0dbff297d04ffb2bcaddea0", "sha256_in_prefix": "e9f06ae605bc54157c55c5d48a9f3981870eb44ab0dbff297d04ffb2bcaddea0", "size_in_bytes": 371507}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/interKernelCommunication/README.TXT", "path_type": "hardlink", "sha256": "734dddaf9b78dcb4d185068c95dbd1abdeb163c18fb3c9a28b2175dd279f6a70", "sha256_in_prefix": "734dddaf9b78dcb4d185068c95dbd1abdeb163c18fb3c9a28b2175dd279f6a70", "size_in_bytes": 4787}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/interKernelCommunication/interKernelCommunication.cu", "path_type": "hardlink", "sha256": "3538719f5adae0b6656e458391e2e08ccc410b4a0c5e1439394c3f01ecea7852", "sha256_in_prefix": "3538719f5adae0b6656e458391e2e08ccc410b4a0c5e1439394c3f01ecea7852", "size_in_bytes": 14254}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/README.TXT", "path_type": "hardlink", "sha256": "34db81af4c0e6ad54d0ef45030edeeef5542c0dfe5fc1071001b7f271174c90e", "sha256_in_prefix": "34db81af4c0e6ad54d0ef45030edeeef5542c0dfe5fc1071001b7f271174c90e", "size_in_bytes": 2702}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/sharedBankConflicts.cu", "path_type": "hardlink", "sha256": "1295906108d4bd24ab86a5134308558b7fe2645f0aab0a25c3986527706a62cc", "sha256_in_prefix": "1295906108d4bd24ab86a5134308558b7fe2645f0aab0a25c3986527706a62cc", "size_in_bytes": 10229}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/sharedBankConflicts.pdf", "path_type": "hardlink", "sha256": "bbdd9bb82e0bba6efa18adec93a0efa7b3e1bb8af58e3688288836be32c46a65", "sha256_in_prefix": "bbdd9bb82e0bba6efa18adec93a0efa7b3e1bb8af58e3688288836be32c46a65", "size_in_bytes": 3867051}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/transposeCoalesced.ncu-rep", "path_type": "hardlink", "sha256": "c5a0c08e60abde0e20376829a181bdd4a878bcb778f51d05bd8e8fb3a880be00", "sha256_in_prefix": "c5a0c08e60abde0e20376829a181bdd4a878bcb778f51d05bd8e8fb3a880be00", "size_in_bytes": 568389}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/sharedBankConflicts/transposeNoBankConflicts.ncu-rep", "path_type": "hardlink", "sha256": "544b42110730bd2adf2325eeea44a69be6455767443fe1d7a1572bbd7183bb29", "sha256_in_prefix": "544b42110730bd2adf2325eeea44a69be6455767443fe1d7a1572bbd7183bb29", "size_in_bytes": 466431}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/README.TXT", "path_type": "hardlink", "sha256": "4eae3fccfa9c11b384f7911381bd1967cf59ad4e6a7623092d7705a1061a448f", "sha256_in_prefix": "4eae3fccfa9c11b384f7911381bd1967cf59ad4e6a7623092d7705a1061a448f", "size_in_bytes": 2092}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/addConstDouble.ncu-rep", "path_type": "hardlink", "sha256": "3afffc2e1a1482477ad784c83a1243ccec5ce63c1bb278e7315b698f962fcfcd", "sha256_in_prefix": "3afffc2e1a1482477ad784c83a1243ccec5ce63c1bb278e7315b698f962fcfcd", "size_in_bytes": 277780}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/addConstDouble3.ncu-rep", "path_type": "hardlink", "sha256": "f9ddc7def4da494eebaf0bef7df8a20a0ae14026c62dc3ff4c116fbd8fea521f", "sha256_in_prefix": "f9ddc7def4da494eebaf0bef7df8a20a0ae14026c62dc3ff4c116fbd8fea521f", "size_in_bytes": 285545}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/uncoalescedGlobalAccesses.cu", "path_type": "hardlink", "sha256": "5f314dea5facc9f4b4ee6147033170b12209fe93dfea2aa6e850e57c58960330", "sha256_in_prefix": "5f314dea5facc9f4b4ee6147033170b12209fe93dfea2aa6e850e57c58960330", "size_in_bytes": 6616}, {"_path": "Library/nsight-compute/2024.1.1/extras/samples/uncoalescedGlobalAccesses/uncoalescedGlobalAccesses.pdf", "path_type": "hardlink", "sha256": "7c81c12c0f08150e470e262175c582e3dbbaf439ccf4ae713c70634bb3564566", "sha256_in_prefix": "7c81c12c0f08150e470e262175c582e3dbbaf439ccf4ae713c70634bb3564566", "size_in_bytes": 4300148}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/CudaGpuInfoDumper.exe", "path_type": "hardlink", "sha256": "ff4028c8ad178c56ca913b0699d22409f80059f3db2ebc470f45c6c3d6b81c5b", "sha256_in_prefix": "ff4028c8ad178c56ca913b0699d22409f80059f3db2ebc470f45c6c3d6b81c5b", "size_in_bytes": 708704}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ad10x-gfxt.config", "path_type": "hardlink", "sha256": "7c6175e6601f31cac4ce6875ce078d30eb32381aacf952c1ba45135e3191b6ac", "sha256_in_prefix": "7c6175e6601f31cac4ce6875ce078d30eb32381aacf952c1ba45135e3191b6ac", "size_in_bytes": 25458}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ad10x.config", "path_type": "hardlink", "sha256": "7eb80f84857fd031f4ce9c394479b81f6f614e96720d4d60eec10405bd3cd5db", "sha256_in_prefix": "7eb80f84857fd031f4ce9c394479b81f6f614e96720d4d60eec10405bd3cd5db", "size_in_bytes": 7597}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga100.config", "path_type": "hardlink", "sha256": "35357807f61ec38bd993c9ca247b9404cfbc501a2f837219c10fa7ef11571815", "sha256_in_prefix": "35357807f61ec38bd993c9ca247b9404cfbc501a2f837219c10fa7ef11571815", "size_in_bytes": 9195}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga10b.config", "path_type": "hardlink", "sha256": "a4b95ca287e28e546948473c567c356afdbd7a23d5d82960c3796e2a2aa32a65", "sha256_in_prefix": "a4b95ca287e28e546948473c567c356afdbd7a23d5d82960c3796e2a2aa32a65", "size_in_bytes": 5396}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga10x-gfxact.config", "path_type": "hardlink", "sha256": "a20447a3410fff21c5b896bd1d6c1b75bf0c6744e068909d2c3ee18f62695961", "sha256_in_prefix": "a20447a3410fff21c5b896bd1d6c1b75bf0c6744e068909d2c3ee18f62695961", "size_in_bytes": 24109}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga10x-gfxt.config", "path_type": "hardlink", "sha256": "8c88e8c091e03b8113cd93e8c5f06ac0b8f0ed7d0a2b8c3ae4508ffc3d1b2b5f", "sha256_in_prefix": "8c88e8c091e03b8113cd93e8c5f06ac0b8f0ed7d0a2b8c3ae4508ffc3d1b2b5f", "size_in_bytes": 24984}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/ga10x.config", "path_type": "hardlink", "sha256": "9d6d5515ce80e77493776a3cced3e763897792156ff90e754589f53d5d5c74e7", "sha256_in_prefix": "9d6d5515ce80e77493776a3cced3e763897792156ff90e754589f53d5d5c74e7", "size_in_bytes": 10511}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/gh100.config", "path_type": "hardlink", "sha256": "99c7e205cee947ba4e4109e7e33cea832a3a0e363c26a5fc79373abe859a10ba", "sha256_in_prefix": "99c7e205cee947ba4e4109e7e33cea832a3a0e363c26a5fc79373abe859a10ba", "size_in_bytes": 9195}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/index.config", "path_type": "hardlink", "sha256": "fd7428e6226633aed8660555b18f22e5c5ffd28e4cb181e5b58f362c5e1c77b8", "sha256_in_prefix": "fd7428e6226633aed8660555b18f22e5c5ffd28e4cb181e5b58f362c5e1c77b8", "size_in_bytes": 99}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/tu10x-gfxt.config", "path_type": "hardlink", "sha256": "88627cdde91b92e91ff0edc5e7aa55becd6ce8d6e40058ce1a4b0d013c468fe9", "sha256_in_prefix": "88627cdde91b92e91ff0edc5e7aa55becd6ce8d6e40058ce1a4b0d013c468fe9", "size_in_bytes": 17519}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/tu10x.config", "path_type": "hardlink", "sha256": "69fdcd46b7131fd46c2d6be18c748e79a9ed38aa14b8638d05e125903ed86076", "sha256_in_prefix": "69fdcd46b7131fd46c2d6be18c748e79a9ed38aa14b8638d05e125903ed86076", "size_in_bytes": 11182}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/GpuMetrics/tu11x.config", "path_type": "hardlink", "sha256": "5a8c14c72afd22a88d3a58ca7b9e76185bf6ba6779ec46eb55d611e1f9b5ef1c", "sha256_in_prefix": "5a8c14c72afd22a88d3a58ca7b9e76185bf6ba6779ec46eb55d611e1f9b5ef1c", "size_in_bytes": 7947}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/NsysVsIntegration.xml", "path_type": "hardlink", "sha256": "9544284717a53ce117a1f2fa985b08b4455bb8061ac0731b2db6e320669141a9", "sha256_in_prefix": "9544284717a53ce117a1f2fa985b08b4455bb8061ac0731b2db6e320669141a9", "size_in_bytes": 1112}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/PythonNvtx/annotations.json", "path_type": "hardlink", "sha256": "3bbb714b310c8bb30c776daedbb73588be702e5f0605db47d99526b9e220dce9", "sha256_in_prefix": "3bbb714b310c8bb30c776daedbb73588be702e5f0605db47d99526b9e220dce9", "size_in_bytes": 1128}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjection64.dll", "path_type": "hardlink", "sha256": "9fac19b68548f4ead200e0b510a3f98dd2a29176bd73e4dcf584b309631834c6", "sha256_in_prefix": "9fac19b68548f4ead200e0b510a3f98dd2a29176bd73e4dcf584b309631834c6", "size_in_bytes": 11714640}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionHelper64.dll", "path_type": "hardlink", "sha256": "33f29eaafffc28069a0ee5be923b5756ec945f72ce384ec02b171d1971c3bfda", "sha256_in_prefix": "33f29eaafffc28069a0ee5be923b5756ec945f72ce384ec02b171d1971c3bfda", "size_in_bytes": 922176}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionPythonBacktrace64.dll", "path_type": "hardlink", "sha256": "126faf4ca9d61aa67d236ea40972cf6047e311c21bb455a885bbcd5780deadcb", "sha256_in_prefix": "126faf4ca9d61aa67d236ea40972cf6047e311c21bb455a885bbcd5780deadcb", "size_in_bytes": 256576}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionPythonGilTracing64.dll", "path_type": "hardlink", "sha256": "1955355551fbcbf0860ad04a892a39dc50df7fecb1099592501e49e28d92cad2", "sha256_in_prefix": "1955355551fbcbf0860ad04a892a39dc50df7fecb1099592501e49e28d92cad2", "size_in_bytes": 219216}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionPythonNvtxAnnotations64.dll", "path_type": "hardlink", "sha256": "40d8d10ee26ef78968c28aeefb4d1b0d6adacce4a6f16233f7bb02cf1d1b45db", "sha256_in_prefix": "40d8d10ee26ef78968c28aeefb4d1b0d6adacce4a6f16233f7bb02cf1d1b45db", "size_in_bytes": 383040}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/ToolsInjectionWindowsHook64.dll", "path_type": "hardlink", "sha256": "b0fd921370cf3cd1a9c46b2edeebdecbb035c0cf0bc8cfa365fd75cd6e962c78", "sha256_in_prefix": "b0fd921370cf3cd1a9c46b2edeebdecbb035c0cf0bc8cfa365fd75cd6e962c78", "size_in_bytes": 151616}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/arrow.dll", "path_type": "hardlink", "sha256": "63913b0ee2c2ef3335fe09b179ed1222cd339843b229c2058dd9d1c6268951cd", "sha256_in_prefix": "63913b0ee2c2ef3335fe09b179ed1222cd339843b229c2058dd9d1c6268951cd", "size_in_bytes": 8399936}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/bifrost.dll", "path_type": "hardlink", "sha256": "5e52f4c7f534c94699d0ae32ac1e3aeed13b5ff67e5162f0b4ca144900262ef4", "sha256_in_prefix": "5e52f4c7f534c94699d0ae32ac1e3aeed13b5ff67e5162f0b4ca144900262ef4", "size_in_bytes": 2909272}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/bifrost_loader.2.dll", "path_type": "hardlink", "sha256": "64c21f231f53bdeb19cb04293ec5ef8708259dd86996ae56d5e1a675d3a3b969", "sha256_in_prefix": "64c21f231f53bdeb19cb04293ec5ef8708259dd86996ae56d5e1a675d3a3b969", "size_in_bytes": 2122304}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/bifrost_plugin.2.dll", "path_type": "hardlink", "sha256": "57bde6fc5c12cd0b11d90ba7d5a7dccca3f830433e6b0d8955864ed05d65e9b9", "sha256_in_prefix": "57bde6fc5c12cd0b11d90ba7d5a7dccca3f830433e6b0d8955864ed05d65e9b9", "size_in_bytes": 1849944}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/config.ini", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_100.dll", "path_type": "hardlink", "sha256": "9fe7c76ba1a33b0df86a5b59178fd2d7bcf4889aa77fad5759d9105d0c6616b7", "sha256_in_prefix": "9fe7c76ba1a33b0df86a5b59178fd2d7bcf4889aa77fad5759d9105d0c6616b7", "size_in_bytes": 3667024}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_101.dll", "path_type": "hardlink", "sha256": "98bd5008d97bc777f49a4eb4a6caacb26e23a81dbb81a2362d5d5f4e7c42d773", "sha256_in_prefix": "98bd5008d97bc777f49a4eb4a6caacb26e23a81dbb81a2362d5d5f4e7c42d773", "size_in_bytes": 3673664}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_102.dll", "path_type": "hardlink", "sha256": "81505fd0c3d1190685405af927999a2283cbc4b1841918591877337de0f6ea78", "sha256_in_prefix": "81505fd0c3d1190685405af927999a2283cbc4b1841918591877337de0f6ea78", "size_in_bytes": 3539536}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_110.dll", "path_type": "hardlink", "sha256": "588f9e4cd203ccc86e0ead3eaf41a5028b8bfaa48ac2c94ec8443b9bd5cf4300", "sha256_in_prefix": "588f9e4cd203ccc86e0ead3eaf41a5028b8bfaa48ac2c94ec8443b9bd5cf4300", "size_in_bytes": 3748928}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_111.dll", "path_type": "hardlink", "sha256": "731fc7921752f00acc2f8d42900878b510df51370ca68aaeb097dd0f66f23898", "sha256_in_prefix": "731fc7921752f00acc2f8d42900878b510df51370ca68aaeb097dd0f66f23898", "size_in_bytes": 3756624}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_112.dll", "path_type": "hardlink", "sha256": "73ff13f1f76c54645366ea6aed95a1cf5fbbd38ee5c26fdbe5991049e1ef6af8", "sha256_in_prefix": "73ff13f1f76c54645366ea6aed95a1cf5fbbd38ee5c26fdbe5991049e1ef6af8", "size_in_bytes": 3792448}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_113.dll", "path_type": "hardlink", "sha256": "13ff5563cd7694f3e62a1a86d636a5395900a7b35bde4c0a62f8cd3febb71289", "sha256_in_prefix": "13ff5563cd7694f3e62a1a86d636a5395900a7b35bde4c0a62f8cd3febb71289", "size_in_bytes": 3859536}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_114.dll", "path_type": "hardlink", "sha256": "bdfe18e01b39e2654466825d8bc917f27b88daec6c2c9df8b8416156c9aef907", "sha256_in_prefix": "bdfe18e01b39e2654466825d8bc917f27b88daec6c2c9df8b8416156c9aef907", "size_in_bytes": 4116032}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_115.dll", "path_type": "hardlink", "sha256": "b88f1ee941c927d15af59107d7bbc5c6036f5c7b0d88961bfcacf0f9865808c5", "sha256_in_prefix": "b88f1ee941c927d15af59107d7bbc5c6036f5c7b0d88961bfcacf0f9865808c5", "size_in_bytes": 4120144}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_116.dll", "path_type": "hardlink", "sha256": "65e9ee315545aaf4de6d02993846353d75f42b389264890334d794b3067ecd48", "sha256_in_prefix": "65e9ee315545aaf4de6d02993846353d75f42b389264890334d794b3067ecd48", "size_in_bytes": 4120144}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_117.dll", "path_type": "hardlink", "sha256": "2ca6ef0a7fa715bf739f4c4762fe87872b223ac2a7e15a8bbf5c1e07acf43234", "sha256_in_prefix": "2ca6ef0a7fa715bf739f4c4762fe87872b223ac2a7e15a8bbf5c1e07acf43234", "size_in_bytes": 4178496}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_118.dll", "path_type": "hardlink", "sha256": "786ff399270f8de9abf078a90f9b2bb4a415c17bc220d09ff4e5f129d1f5d858", "sha256_in_prefix": "786ff399270f8de9abf078a90f9b2bb4a415c17bc220d09ff4e5f129d1f5d858", "size_in_bytes": 4302416}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/cupti64_124.dll", "path_type": "hardlink", "sha256": "9f595157ee008f91427b1fcb1828723f58a6bb1d2402bab27e444c4922742191", "sha256_in_prefix": "9f595157ee008f91427b1fcb1828723f58a6bb1d2402bab27e444c4922742191", "size_in_bytes": 4025920}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/etw_providers_template.json", "path_type": "hardlink", "sha256": "27c19d05d829052b48ad29f985d7fc8bdb8b85330b6eeca9121c16e3357f776e", "sha256_in_prefix": "27c19d05d829052b48ad29f985d7fc8bdb8b85330b6eeca9121c16e3357f776e", "size_in_bytes": 2645}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nsight-sys-service.exe", "path_type": "hardlink", "sha256": "7c792fc078afce1fe86491c3e88dd16506ae2ac5c73f09cb5130f6bf594b1b3f", "sha256_in_prefix": "7c792fc078afce1fe86491c3e88dd16506ae2ac5c73f09cb5130f6bf594b1b3f", "size_in_bytes": 134208}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nsys.exe", "path_type": "hardlink", "sha256": "1455f209504db50501c14b360959a97ef6021a4351f7f1c8ddfa7a7b7cd99d9b", "sha256_in_prefix": "1455f209504db50501c14b360959a97ef6021a4351f7f1c8ddfa7a7b7cd99d9b", "size_in_bytes": 36569160}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvlog.config.template", "path_type": "hardlink", "sha256": "c69312187c4d0fb19e930d86baa030e07d1e7f0b46d730e6aa609fb542b158a7", "sha256_in_prefix": "c69312187c4d0fb19e930d86baa030e07d1e7f0b46d730e6aa609fb542b158a7", "size_in_bytes": 648}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvperf_grfx_host.dll", "path_type": "hardlink", "sha256": "056d5c3561dd6833d25c13ca933480bce6f90df7a7475f553146bc66fa2c7725", "sha256_in_prefix": "056d5c3561dd6833d25c13ca933480bce6f90df7a7475f553146bc66fa2c7725", "size_in_bytes": 22516312}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvsym.dll", "path_type": "hardlink", "sha256": "70147a7029b02b55e1f31af7236738e62e010d337656ce98fcd723cb319c436b", "sha256_in_prefix": "70147a7029b02b55e1f31af7236738e62e010d337656ce98fcd723cb319c436b", "size_in_bytes": 3771480}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExt.h", "path_type": "hardlink", "sha256": "9efbf763f4573b16224fe6a1391a21c775a9546d0b2021c9c56834193207d7b8", "sha256_in_prefix": "9efbf763f4573b16224fe6a1391a21c775a9546d0b2021c9c56834193207d7b8", "size_in_bytes": 52130}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtCuda.h", "path_type": "hardlink", "sha256": "ad8f066ef118c7efaa7398f40cd3bdf00b4d84b6b056d41cdd832d0cc6dfb83e", "sha256_in_prefix": "ad8f066ef118c7efaa7398f40cd3bdf00b4d84b6b056d41cdd832d0cc6dfb83e", "size_in_bytes": 4758}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtCudaRt.h", "path_type": "hardlink", "sha256": "f5ff06c50ed8da0ac1adc10e87737159890ed82dfa30cba6f83922912ccef19d", "sha256_in_prefix": "f5ff06c50ed8da0ac1adc10e87737159890ed82dfa30cba6f83922912ccef19d", "size_in_bytes": 3923}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtOpenCL.h", "path_type": "hardlink", "sha256": "6f8cca615c0359d25bf12bdce2d95e8d3a949085b3989dd76d7b1ff5adbe29c4", "sha256_in_prefix": "6f8cca615c0359d25bf12bdce2d95e8d3a949085b3989dd76d7b1ff5adbe29c4", "size_in_bytes": 7167}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvToolsExtSync.h", "path_type": "hardlink", "sha256": "c15237974bf60e746e38618e16ff84d7f05c8eaba36e4b6c488abb2b0892bee8", "sha256_in_prefix": "c15237974bf60e746e38618e16ff84d7f05c8eaba36e4b6c488abb2b0892bee8", "size_in_bytes": 13551}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtx3.hpp", "path_type": "hardlink", "sha256": "9ab1daeef16ef8eb49c6b1d8be0c37dde1b9dd9083d6f6d168035aba70e719c6", "sha256_in_prefix": "9ab1daeef16ef8eb49c6b1d8be0c37dde1b9dd9083d6f6d168035aba70e719c6", "size_in_bytes": 103412}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImpl.h", "path_type": "hardlink", "sha256": "bbf8525ae2a64ca4f8dc3c3dd2debbe682ddff7b929ce0129795c56ecb9c4b7e", "sha256_in_prefix": "bbf8525ae2a64ca4f8dc3c3dd2debbe682ddff7b929ce0129795c56ecb9c4b7e", "size_in_bytes": 22104}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCore.h", "path_type": "hardlink", "sha256": "d525f715d9cfa83455af56faf7665fb800122665883818130dd2c0380b89f282", "sha256_in_prefix": "d525f715d9cfa83455af56faf7665fb800122665883818130dd2c0380b89f282", "size_in_bytes": 10318}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCudaRt_v3.h", "path_type": "hardlink", "sha256": "126b40c3eb43042702d11e753b20318528f707772c391f184ba8569e467f8277", "sha256_in_prefix": "126b40c3eb43042702d11e753b20318528f707772c391f184ba8569e467f8277", "size_in_bytes": 3185}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplCuda_v3.h", "path_type": "hardlink", "sha256": "dfba45b8d8e231f9c6eee0827a59980e049ea21f1919a550a6ca1e1f0ec2bbee", "sha256_in_prefix": "dfba45b8d8e231f9c6eee0827a59980e049ea21f1919a550a6ca1e1f0ec2bbee", "size_in_bytes": 3985}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplOpenCL_v3.h", "path_type": "hardlink", "sha256": "3cc35f21fc467ad63aed9abd8d34b888490f0dd44917f0032fe7f9dcfacee34d", "sha256_in_prefix": "3cc35f21fc467ad63aed9abd8d34b888490f0dd44917f0032fe7f9dcfacee34d", "size_in_bytes": 6769}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxImplSync_v3.h", "path_type": "hardlink", "sha256": "a2a8df970034b58ac263d0d7bc0244d31e113bb6178f257fa0584c6dbd278a6f", "sha256_in_prefix": "a2a8df970034b58ac263d0d7bc0244d31e113bb6178f257fa0584c6dbd278a6f", "size_in_bytes": 3438}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInit.h", "path_type": "hardlink", "sha256": "7c56afb96a2117859cec7f6f875b0c5cb3b50d1a63d7ec4ad675a80e17217a1f", "sha256_in_prefix": "7c56afb96a2117859cec7f6f875b0c5cb3b50d1a63d7ec4ad675a80e17217a1f", "size_in_bytes": 13361}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInitDecls.h", "path_type": "hardlink", "sha256": "d9fd0ce350f2a7d4a58cf6e4c4773456b259b9c78f580ddd1643df798f7730b5", "sha256_in_prefix": "d9fd0ce350f2a7d4a58cf6e4c4773456b259b9c78f580ddd1643df798f7730b5", "size_in_bytes": 9697}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxInitDefs.h", "path_type": "hardlink", "sha256": "e8d0fad60f7c858e2ef0685a3ea6fdc8187a9f5f8162a13e934a20103fccd759", "sha256_in_prefix": "e8d0fad60f7c858e2ef0685a3ea6fdc8187a9f5f8162a13e934a20103fccd759", "size_in_bytes": 36260}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxLinkOnce.h", "path_type": "hardlink", "sha256": "e1d11108ca20e34e3d4638515a1a30351c532efa637cf4640b886c619c12a305", "sha256_in_prefix": "e1d11108ca20e34e3d4638515a1a30351c532efa637cf4640b886c619c12a305", "size_in_bytes": 4201}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/nvtx/include/nvtx3/nvtxDetail/nvtxTypes.h", "path_type": "hardlink", "sha256": "21e7d502e952b392ad059a4763a6d3f45ebe1f5e4f5a514c544abe9a81df2089", "sha256_in_prefix": "21e7d502e952b392ad059a4763a6d3f45ebe1f5e4f5a514c544abe9a81df2089", "size_in_bytes": 16069}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/parquet.dll", "path_type": "hardlink", "sha256": "049826b16a4a928dd8085dc80cf765e61e5207b7eedc2b0776e70f261981632b", "sha256_in_prefix": "049826b16a4a928dd8085dc80cf765e61e5207b7eedc2b0776e70f261981632b", "size_in_bytes": 1736768}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/bin/python.exe", "path_type": "hardlink", "sha256": "8c9336f86f8685e9cecd8682b27f84699dcfd9f0381d27d8c476c4749c532ccf", "sha256_in_prefix": "8c9336f86f8685e9cecd8682b27f84699dcfd9f0381d27d8c476c4749c532ccf", "size_in_bytes": 9810944}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/lib/gpustats.py", "path_type": "hardlink", "sha256": "04e2058370cdb9ef1e766143777255bfaf1a4ec92fb584f741240e763312b18b", "sha256_in_prefix": "04e2058370cdb9ef1e766143777255bfaf1a4ec92fb584f741240e763312b18b", "size_in_bytes": 8987}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/lib/kernel_helper.py", "path_type": "hardlink", "sha256": "fd1749b894eb3619a51bcd9ca42055d436716a23f6d22896ce56cb2cdd8466ca", "sha256_in_prefix": "fd1749b894eb3619a51bcd9ca42055d436716a23f6d22896ce56cb2cdd8466ca", "size_in_bytes": 6117}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/lib/nsysstats.py", "path_type": "hardlink", "sha256": "74d1d66742e6c12fdbb5da01e1bd331f60c2ce8fd029e134665c961d9e6c8a66", "sha256_in_prefix": "74d1d66742e6c12fdbb5da01e1bd331f60c2ce8fd029e134665c961d9e6c8a66", "size_in_bytes": 21024}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/Dockerfile", "path_type": "hardlink", "sha256": "d5138fd9bf5edc74cac8eb4f5b0b4a24d67993b631086c26fc064a2be8b703b7", "sha256_in_prefix": "d5138fd9bf5edc74cac8eb4f5b0b4a24d67993b631086c26fc064a2be8b703b7", "size_in_bytes": 735}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/__init__.py", "path_type": "hardlink", "sha256": "96c37ac089f13c8a8a7d6610789ed5de39e5358c195fb4970d052bc6b2624979", "sha256_in_prefix": "96c37ac089f13c8a8a7d6610789ed5de39e5358c195fb4970d052bc6b2624979", "size_in_bytes": 1993}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/__main__.py", "path_type": "hardlink", "sha256": "d264d8901b0db66961a7bcefede6fed692f4d8af248762f9bcccec64632fd78e", "sha256_in_prefix": "d264d8901b0db66961a7bcefede6fed692f4d8af248762f9bcccec64632fd78e", "size_in_bytes": 4294}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/clean.py", "path_type": "hardlink", "sha256": "1cd728caa1ce6f0033908484f3c462c1cdaa228e2c71d6a662a6b9aadb5db40d", "sha256_in_prefix": "1cd728caa1ce6f0033908484f3c462c1cdaa228e2c71d6a662a6b9aadb5db40d", "size_in_bytes": 876}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/data_service.py", "path_type": "hardlink", "sha256": "357f03594dd22dc3d51bffa779a4cab70961be4ad50a1e30694762364ecc0ee3", "sha256_in_prefix": "357f03594dd22dc3d51bffa779a4cab70961be4ad50a1e30694762364ecc0ee3", "size_in_bytes": 19026}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/format.py", "path_type": "hardlink", "sha256": "eeeea1c54cb8986206fa4d037bc4594ddbf35d15c3f6b877e699540e61c20344", "sha256_in_prefix": "eeeea1c54cb8986206fa4d037bc4594ddbf35d15c3f6b877e699540e61c20344", "size_in_bytes": 719}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/install.py", "path_type": "hardlink", "sha256": "862c88eab7683ac1d36fe5fdc20625184b754434ccbac65228ce8300bd8f587a", "sha256_in_prefix": "862c88eab7683ac1d36fe5fdc20625184b754434ccbac65228ce8300bd8f587a", "size_in_bytes": 7301}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/__init__.py", "path_type": "hardlink", "sha256": "4bf3d4f9f39f88d2903766a9950d323f4372112eb080b6b501e958f8d66eeaca", "sha256_in_prefix": "4bf3d4f9f39f88d2903766a9950d323f4372112eb080b6b501e958f8d66eeaca", "size_in_bytes": 830}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/args.py", "path_type": "hardlink", "sha256": "d635fd98befdca16e703afbc978465423d5e70ccac60966da15bb4f4686e8496", "sha256_in_prefix": "d635fd98befdca16e703afbc978465423d5e70ccac60966da15bb4f4686e8496", "size_in_bytes": 11018}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/collective_loader.py", "path_type": "hardlink", "sha256": "7e35c1fa599edf89489eba49da74268867fc4e12ca1c5730a66a1f726abdeaed", "sha256_in_prefix": "7e35c1fa599edf89489eba49da74268867fc4e12ca1c5730a66a1f726abdeaed", "size_in_bytes": 2788}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/exceptions.py", "path_type": "hardlink", "sha256": "cab6df53143127ac111924928245beaa8039844c7149c8961f818f817c760cfe", "sha256_in_prefix": "cab6df53143127ac111924928245beaa8039844c7149c8961f818f817c760cfe", "size_in_bytes": 959}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/heatmap.py", "path_type": "hardlink", "sha256": "cc5de8b9cd9ce6a1975a8d11199a865f3703dee56f7d8508f35a24b9e37a86a0", "sha256_in_prefix": "cc5de8b9cd9ce6a1975a8d11199a865f3703dee56f7d8508f35a24b9e37a86a0", "size_in_bytes": 6207}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/helpers.py", "path_type": "hardlink", "sha256": "f7297cee30ed6dde78dfc07cc852d3051d8c9208b7c9d13323df2f16ddcddb14", "sha256_in_prefix": "f7297cee30ed6dde78dfc07cc852d3051d8c9208b7c9d13323df2f16ddcddb14", "size_in_bytes": 1836}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_display.py", "path_type": "hardlink", "sha256": "08dffe26c813799434d877eef80839c843fdde88fc4c83342814b0a6266384c8", "sha256_in_prefix": "08dffe26c813799434d877eef80839c843fdde88fc4c83342814b0a6266384c8", "size_in_bytes": 5959}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_path.py", "path_type": "hardlink", "sha256": "5bc2cc47a9724ae8b278c478b92d8f67f00612428fee71b4b0aac13f0689101f", "sha256_in_prefix": "5bc2cc47a9724ae8b278c478b92d8f67f00612428fee71b4b0aac13f0689101f", "size_in_bytes": 1822}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nsys_pres.py", "path_type": "hardlink", "sha256": "2e6ececb75ceaee1aace120b3a8c2813cade522472b3bfa6eed54fa788484612", "sha256_in_prefix": "2e6ececb75ceaee1aace120b3a8c2813cade522472b3bfa6eed54fa788484612", "size_in_bytes": 12009}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/nvtx.py", "path_type": "hardlink", "sha256": "edfeae9b9b949fd32be4b31797d7873c2478ffcb3bc771fb1dc97beaa96395d0", "sha256_in_prefix": "edfeae9b9b949fd32be4b31797d7873c2478ffcb3bc771fb1dc97beaa96395d0", "size_in_bytes": 4746}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/pace.py", "path_type": "hardlink", "sha256": "bb75422ce5acdedcf60c132b5fe69a13424a78d842bee914ba6f1340acf83575", "sha256_in_prefix": "bb75422ce5acdedcf60c132b5fe69a13424a78d842bee914ba6f1340acf83575", "size_in_bytes": 3668}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/recipe.py", "path_type": "hardlink", "sha256": "ccf108cf247424725efec5f7a93a17f9bdd77b0e443ceea0f32c3958f5efd47e", "sha256_in_prefix": "ccf108cf247424725efec5f7a93a17f9bdd77b0e443ceea0f32c3958f5efd47e", "size_in_bytes": 17183}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/recipe_loader.py", "path_type": "hardlink", "sha256": "fe7a1b1205e06067d6de0354167336c7bd9266d68cda53088c3dce2d0ea59950", "sha256_in_prefix": "fe7a1b1205e06067d6de0354167336c7bd9266d68cda53088c3dce2d0ea59950", "size_in_bytes": 3309}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/lib/summary.py", "path_type": "hardlink", "sha256": "cb17987f7287aede49731a6d28454514102ad884ab382b5b73485509a04933c1", "sha256_in_prefix": "cb17987f7287aede49731a6d28454514102ad884ab382b5b73485509a04933c1", "size_in_bytes": 3350}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/log.py", "path_type": "hardlink", "sha256": "5e5e840036500193e34904d8832416964ab6f48551dc671c0e4d4b6105007eb3", "sha256_in_prefix": "5e5e840036500193e34904d8832416964ab6f48551dc671c0e4d4b6105007eb3", "size_in_bytes": 1736}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/nsys_constants.py", "path_type": "hardlink", "sha256": "f838e299e973b42a0c17c584384264ed1ce636ec3d3a628a3a67b1dd808cd66a", "sha256_in_prefix": "f838e299e973b42a0c17c584384264ed1ce636ec3d3a628a3a67b1dd808cd66a", "size_in_bytes": 2751}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/pyproject.toml", "path_type": "hardlink", "sha256": "7dde595903dfd26688227a7220ae6d029be46620903e50f22fa24c31428d775a", "sha256_in_prefix": "7dde595903dfd26688227a7220ae6d029be46620903e50f22fa24c31428d775a", "size_in_bytes": 85}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/cuda_api_sum.py", "path_type": "hardlink", "sha256": "fe4ccfcc6f78a0d06bf91a2c4d1e5d6fbf3a2fe98f59b93e75e9922ac217359e", "sha256_in_prefix": "fe4ccfcc6f78a0d06bf91a2c4d1e5d6fbf3a2fe98f59b93e75e9922ac217359e", "size_in_bytes": 5111}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/metadata.json", "path_type": "hardlink", "sha256": "898babb2824a68c5facde8245c7cf173196bca72d8c3a60deac3d797720ee119", "sha256_in_prefix": "898babb2824a68c5facde8245c7cf173196bca72d8c3a60deac3d797720ee119", "size_in_bytes": 207}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sum/stats.ipynb", "path_type": "hardlink", "sha256": "4d5680a8ad2fa43ace6c4c95c96043ca94041c45c16bc4a6ccd077e86dc49a2f", "sha256_in_prefix": "4d5680a8ad2fa43ace6c4c95c96043ca94041c45c16bc4a6ccd077e86dc49a2f", "size_in_bytes": 4610}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/analysis.ipynb", "path_type": "hardlink", "sha256": "d6fa3030df92c86a3849d40e8149f76508d9ad38d4adb7fc5bd4741c643d0c72", "sha256_in_prefix": "d6fa3030df92c86a3849d40e8149f76508d9ad38d4adb7fc5bd4741c643d0c72", "size_in_bytes": 2037}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/cuda_api_sync.py", "path_type": "hardlink", "sha256": "4ff3f1acb052b32d4a9aea92d0f20eea40cf7228b2f126259a978807ad0521ae", "sha256_in_prefix": "4ff3f1acb052b32d4a9aea92d0f20eea40cf7228b2f126259a978807ad0521ae", "size_in_bytes": 3193}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_api_sync/metadata.json", "path_type": "hardlink", "sha256": "1c13b833f95b8268d1ac57b919a9f1892b7ab3ef7a00ea17ec1f30758de27d70", "sha256_in_prefix": "1c13b833f95b8268d1ac57b919a9f1892b7ab3ef7a00ea17ec1f30758de27d70", "size_in_bytes": 346}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/cuda_gpu_kern_pace.py", "path_type": "hardlink", "sha256": "4eb471af00efcda8698faff3f4397c39d9c98b27c8b87c4927286725ec5d1f3d", "sha256_in_prefix": "4eb471af00efcda8698faff3f4397c39d9c98b27c8b87c4927286725ec5d1f3d", "size_in_bytes": 4674}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/metadata.json", "path_type": "hardlink", "sha256": "1b1568daeaf10154ab641f0b8ed69b3cf0c447bbca4d4e271e1135e13fb47b77", "sha256_in_prefix": "1b1568daeaf10154ab641f0b8ed69b3cf0c447bbca4d4e271e1135e13fb47b77", "size_in_bytes": 258}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_pace/pace.ipynb", "path_type": "hardlink", "sha256": "a26002c6148a7e4ffa0ab3ae95dbac6ff52a3f5295f97a9bd28ede1620750fe6", "sha256_in_prefix": "a26002c6148a7e4ffa0ab3ae95dbac6ff52a3f5295f97a9bd28ede1620750fe6", "size_in_bytes": 11502}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/cuda_gpu_kern_sum.py", "path_type": "hardlink", "sha256": "0bcae3e12ac3d488673d423f45caf65e86ca0bab8bbf0f90ff6ad4ddd271e0ae", "sha256_in_prefix": "0bcae3e12ac3d488673d423f45caf65e86ca0bab8bbf0f90ff6ad4ddd271e0ae", "size_in_bytes": 5212}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/metadata.json", "path_type": "hardlink", "sha256": "7b9bf96da15cb7bb9ee3342d9da49d62b4098557d8d88aef0f54f126e5867532", "sha256_in_prefix": "7b9bf96da15cb7bb9ee3342d9da49d62b4098557d8d88aef0f54f126e5867532", "size_in_bytes": 213}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_kern_sum/stats.ipynb", "path_type": "hardlink", "sha256": "dd40c479a6e894fd873adf9acfe7d2b38322fa437f1f4957d963a29ec4dff370", "sha256_in_prefix": "dd40c479a6e894fd873adf9acfe7d2b38322fa437f1f4957d963a29ec4dff370", "size_in_bytes": 4588}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/cuda_gpu_mem_size_sum.py", "path_type": "hardlink", "sha256": "9b41fd33b9ef136dbfbcbae814b83b159b9cfba2a42194c6a1bc2019f7d2c16a", "sha256_in_prefix": "9b41fd33b9ef136dbfbcbae814b83b159b9cfba2a42194c6a1bc2019f7d2c16a", "size_in_bytes": 4812}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/metadata.json", "path_type": "hardlink", "sha256": "0187893c2a456e6c8d2eddbdf4c19fdc04ac19eb34c98ad827189847ba4f2777", "sha256_in_prefix": "0187893c2a456e6c8d2eddbdf4c19fdc04ac19eb34c98ad827189847ba4f2777", "size_in_bytes": 279}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_size_sum/stats.ipynb", "path_type": "hardlink", "sha256": "3520519caa730c55d1050c25f03a3cb1f42bd29cae9548b6e70fd74b957092a6", "sha256_in_prefix": "3520519caa730c55d1050c25f03a3cb1f42bd29cae9548b6e70fd74b957092a6", "size_in_bytes": 4164}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/cuda_gpu_mem_time_sum.py", "path_type": "hardlink", "sha256": "057dd5907ff3951a3b68e22665860c760018d38e53414d74b0be5ba23857fb54", "sha256_in_prefix": "057dd5907ff3951a3b68e22665860c760018d38e53414d74b0be5ba23857fb54", "size_in_bytes": 5143}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/metadata.json", "path_type": "hardlink", "sha256": "038b00d2177a594a154559a70e4734931b20cc56b23bf376eefde6d1914f9da4", "sha256_in_prefix": "038b00d2177a594a154559a70e4734931b20cc56b23bf376eefde6d1914f9da4", "size_in_bytes": 267}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_mem_time_sum/stats.ipynb", "path_type": "hardlink", "sha256": "47117ade84ae2dd8bfb3f415509b44af26ebd9d242a8bd0cb47da4ebf4780752", "sha256_in_prefix": "47117ade84ae2dd8bfb3f415509b44af26ebd9d242a8bd0cb47da4ebf4780752", "size_in_bytes": 4640}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/cuda_gpu_time_util_map.py", "path_type": "hardlink", "sha256": "cf1274c040c3aa09c7b08f24fd2a4332d856a438e25ed916ef577e40d7a27256", "sha256_in_prefix": "cf1274c040c3aa09c7b08f24fd2a4332d856a438e25ed916ef577e40d7a27256", "size_in_bytes": 5391}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "816b09c928554492145ad1a4a0108977c2b37a17ce3514c158f7d18e6bb324f7", "sha256_in_prefix": "816b09c928554492145ad1a4a0108977c2b37a17ce3514c158f7d18e6bb324f7", "size_in_bytes": 2572}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_gpu_time_util_map/metadata.json", "path_type": "hardlink", "sha256": "82725e6d776e4b9170d8ddec994be87d514b938d72daf7acf82cd629005bfb2e", "sha256_in_prefix": "82725e6d776e4b9170d8ddec994be87d514b938d72daf7acf82cd629005bfb2e", "size_in_bytes": 568}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/analysis.ipynb", "path_type": "hardlink", "sha256": "5bfb5747f0732307cd983616f233f28eacf2e029794c643b7f8d3af9d7ece621", "sha256_in_prefix": "5bfb5747f0732307cd983616f233f28eacf2e029794c643b7f8d3af9d7ece621", "size_in_bytes": 1883}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/cuda_memcpy_async.py", "path_type": "hardlink", "sha256": "57077a03a1f5704bfe721f210a351ccc85a8c6d013dea2500a7b0a533df4ebd0", "sha256_in_prefix": "57077a03a1f5704bfe721f210a351ccc85a8c6d013dea2500a7b0a533df4ebd0", "size_in_bytes": 3229}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_async/metadata.json", "path_type": "hardlink", "sha256": "a8dbbbcc9f69199a41b07c8b0a6c793bc4fd7a7d31b16142faee8f17627ffc98", "sha256_in_prefix": "a8dbbbcc9f69199a41b07c8b0a6c793bc4fd7a7d31b16142faee8f17627ffc98", "size_in_bytes": 276}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/analysis.ipynb", "path_type": "hardlink", "sha256": "2d6a86c42d7a75e1bbc3ccc699ecad99c24307e55d796ad85820e334aa177071", "sha256_in_prefix": "2d6a86c42d7a75e1bbc3ccc699ecad99c24307e55d796ad85820e334aa177071", "size_in_bytes": 1907}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/cuda_memcpy_sync.py", "path_type": "hardlink", "sha256": "03537fb531b559a0cad22aeba28f7898b451c6c00876d0c6755508f026a189f2", "sha256_in_prefix": "03537fb531b559a0cad22aeba28f7898b451c6c00876d0c6755508f026a189f2", "size_in_bytes": 3202}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memcpy_sync/metadata.json", "path_type": "hardlink", "sha256": "9c4a1890fd46c952e33a09b32cb1e112b59fe6f3598a07c1c19cee0e064e409c", "sha256_in_prefix": "9c4a1890fd46c952e33a09b32cb1e112b59fe6f3598a07c1c19cee0e064e409c", "size_in_bytes": 369}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/analysis.ipynb", "path_type": "hardlink", "sha256": "4585737b6f7214183c90e865faf9ec23735750fcbf82e8cf60b7131c67d8dda1", "sha256_in_prefix": "4585737b6f7214183c90e865faf9ec23735750fcbf82e8cf60b7131c67d8dda1", "size_in_bytes": 2008}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/cuda_memset_sync.py", "path_type": "hardlink", "sha256": "7eaa9fcd632f7ce140ba8cf17fd788eb0c83bf86b8e899461eadccffafc370ca", "sha256_in_prefix": "7eaa9fcd632f7ce140ba8cf17fd788eb0c83bf86b8e899461eadccffafc370ca", "size_in_bytes": 3202}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/cuda_memset_sync/metadata.json", "path_type": "hardlink", "sha256": "2fae3bf5f6a082279a6f4e0de9d7888b29151e277cdf471dedbde2b6f944b80a", "sha256_in_prefix": "2fae3bf5f6a082279a6f4e0de9d7888b29151e277cdf471dedbde2b6f944b80a", "size_in_bytes": 250}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/diff.ipynb", "path_type": "hardlink", "sha256": "9d96177e011506f1cbb22e02f97d70b1d00dd0064638c3ca39239aeb635f59fd", "sha256_in_prefix": "9d96177e011506f1cbb22e02f97d70b1d00dd0064638c3ca39239aeb635f59fd", "size_in_bytes": 3558}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/diff.py", "path_type": "hardlink", "sha256": "0a03969a91d2b26192ecbf2fd3bc95a171096acb7fe3ae02aaf227dad9fe3393", "sha256_in_prefix": "0a03969a91d2b26192ecbf2fd3bc95a171096acb7fe3ae02aaf227dad9fe3393", "size_in_bytes": 7830}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/diff/metadata.json", "path_type": "hardlink", "sha256": "245a6f632df1bd2b44c36c91b5fe85a3a024f369c3c6b04b06555579f47965f6", "sha256_in_prefix": "245a6f632df1bd2b44c36c91b5fe85a3a024f369c3c6b04b06555579f47965f6", "size_in_bytes": 171}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/analysis.ipynb", "path_type": "hardlink", "sha256": "44c5138d0d37c69a5bfc5736fd2c879f34a59951514ccecd33f62ce1787e0402", "sha256_in_prefix": "44c5138d0d37c69a5bfc5736fd2c879f34a59951514ccecd33f62ce1787e0402", "size_in_bytes": 3748}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/dx12_mem_ops.py", "path_type": "hardlink", "sha256": "05ddb3556828fd06e83b6da8e6130364616e2020333c712cbb65729950cf2c8b", "sha256_in_prefix": "05ddb3556828fd06e83b6da8e6130364616e2020333c712cbb65729950cf2c8b", "size_in_bytes": 3190}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/dx12_mem_ops/metadata.json", "path_type": "hardlink", "sha256": "ec9028512e6a9e8ea1b4631ae610d9ae69f7d0b73adc0b0752dcab0152741df5", "sha256_in_prefix": "ec9028512e6a9e8ea1b4631ae610d9ae69f7d0b73adc0b0752dcab0152741df5", "size_in_bytes": 724}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/analysis.ipynb", "path_type": "hardlink", "sha256": "1d10807a02d4b68dadfc04a85faf40a5a0a4a6c2856c594b872580292a63ff1e", "sha256_in_prefix": "1d10807a02d4b68dadfc04a85faf40a5a0a4a6c2856c594b872580292a63ff1e", "size_in_bytes": 2038}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/gpu_gaps.py", "path_type": "hardlink", "sha256": "23723653d4f2f860b1c9f4bf76da891d94727a8abd0a96519f171bec55c79fbf", "sha256_in_prefix": "23723653d4f2f860b1c9f4bf76da891d94727a8abd0a96519f171bec55c79fbf", "size_in_bytes": 3399}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_gaps/metadata.json", "path_type": "hardlink", "sha256": "b6b0bba376db9e657e08c2d85b880b647c4000218694512dd7b1368076afeda2", "sha256_in_prefix": "b6b0bba376db9e657e08c2d85b880b647c4000218694512dd7b1368076afeda2", "size_in_bytes": 591}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/gpu_metric_util_map.py", "path_type": "hardlink", "sha256": "d7a239cdf768dc18d72ae9d39e508947d878a53c6b2660abdd108c1536d0fb38", "sha256_in_prefix": "d7a239cdf768dc18d72ae9d39e508947d878a53c6b2660abdd108c1536d0fb38", "size_in_bytes": 5447}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "c1e9f4629adc50a012d37da8b1478db79e6a7459f839a7edd62ae9855acfa455", "sha256_in_prefix": "c1e9f4629adc50a012d37da8b1478db79e6a7459f839a7edd62ae9855acfa455", "size_in_bytes": 2697}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_metric_util_map/metadata.json", "path_type": "hardlink", "sha256": "c0ba18319b27b9b54841f9da09f998c3a9050942e1e000384e4f1473738f2476", "sha256_in_prefix": "c0ba18319b27b9b54841f9da09f998c3a9050942e1e000384e4f1473738f2476", "size_in_bytes": 246}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/analysis.ipynb", "path_type": "hardlink", "sha256": "919a1decb2b6d5be6a9e0a96db5ae2e6e6b4a65118ccb11012d7853f5db83a52", "sha256_in_prefix": "919a1decb2b6d5be6a9e0a96db5ae2e6e6b4a65118ccb11012d7853f5db83a52", "size_in_bytes": 3013}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/gpu_time_util.py", "path_type": "hardlink", "sha256": "a37609f16883616853790873ba8b6835c16aaeca716360f1aa0f5cbcc7847fae", "sha256_in_prefix": "a37609f16883616853790873ba8b6835c16aaeca716360f1aa0f5cbcc7847fae", "size_in_bytes": 3624}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/gpu_time_util/metadata.json", "path_type": "hardlink", "sha256": "f0d0bee12b848ae938094332f7542a8cc2d4ed3272957d9a24a4c7560428d949", "sha256_in_prefix": "f0d0bee12b848ae938094332f7542a8cc2d4ed3272957d9a24a4c7560428d949", "size_in_bytes": 1547}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "a649f851cdf692b854fdc99b0820912870b3c0dd6ee123c06cd1c7a608013b04", "sha256_in_prefix": "a649f851cdf692b854fdc99b0820912870b3c0dd6ee123c06cd1c7a608013b04", "size_in_bytes": 2577}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/metadata.json", "path_type": "hardlink", "sha256": "021c67018201872b5e5bcb304b642f7c820579d2452844e0d6d07a72f681643a", "sha256_in_prefix": "021c67018201872b5e5bcb304b642f7c820579d2452844e0d6d07a72f681643a", "size_in_bytes": 764}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_gpu_time_util_map/mpi_gpu_time_util_map.py", "path_type": "hardlink", "sha256": "6ebcfba7ebdf7bc597dc07a50422b78046451c8a536a57cf36006c42a582f69b", "sha256_in_prefix": "6ebcfba7ebdf7bc597dc07a50422b78046451c8a536a57cf36006c42a582f69b", "size_in_bytes": 7155}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/metadata.json", "path_type": "hardlink", "sha256": "517374fda80cbe46bc4f5acdaaeb0b3d43d6fdfa083447c7ebbf4a9f91dec761", "sha256_in_prefix": "517374fda80cbe46bc4f5acdaaeb0b3d43d6fdfa083447c7ebbf4a9f91dec761", "size_in_bytes": 192}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/mpi_sum.py", "path_type": "hardlink", "sha256": "5d59f6a38d5b09be0ed3a4dd180db3b574e30f999a9a70d9fa31450b7289c150", "sha256_in_prefix": "5d59f6a38d5b09be0ed3a4dd180db3b574e30f999a9a70d9fa31450b7289c150", "size_in_bytes": 4953}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/mpi_sum/stats.ipynb", "path_type": "hardlink", "sha256": "e80d8c073105e99d1aa1abcbe075b5c5d7f6ee962e209c654e38f167af9d438b", "sha256_in_prefix": "e80d8c073105e99d1aa1abcbe075b5c5d7f6ee962e209c654e38f167af9d438b", "size_in_bytes": 5859}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/metadata.json", "path_type": "hardlink", "sha256": "5b7d54aa297b06f26063bababc0e125d1d6456253f60e86087f47adf089a9df1", "sha256_in_prefix": "5b7d54aa297b06f26063bababc0e125d1d6456253f60e86087f47adf089a9df1", "size_in_bytes": 287}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/nccl_gpu_proj_sum.py", "path_type": "hardlink", "sha256": "bdba19d71df5f2f1eab71ebdee8533c5ea75cea00c6d7a47944f673269818473", "sha256_in_prefix": "bdba19d71df5f2f1eab71ebdee8533c5ea75cea00c6d7a47944f673269818473", "size_in_bytes": 6058}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_proj_sum/stats.ipynb", "path_type": "hardlink", "sha256": "ce7f79e46352aec0aac604d771572462eab4831e313795f5596eb30d367ad93f", "sha256_in_prefix": "ce7f79e46352aec0aac604d771572462eab4831e313795f5596eb30d367ad93f", "size_in_bytes": 6166}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "04ae743f89a6ab88e10aba54d6103b18ed2e2a19552932d32e0dcd8b1b9f8341", "sha256_in_prefix": "04ae743f89a6ab88e10aba54d6103b18ed2e2a19552932d32e0dcd8b1b9f8341", "size_in_bytes": 2636}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/metadata.json", "path_type": "hardlink", "sha256": "420548641448256476a67fa2399f102498016fd75ac12aba7f5889772cb49897", "sha256_in_prefix": "420548641448256476a67fa2399f102498016fd75ac12aba7f5889772cb49897", "size_in_bytes": 580}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_gpu_time_util_map/nccl_gpu_time_util_map.py", "path_type": "hardlink", "sha256": "112f63fce78acdaaf16b44d8c27fda1b6710a1eb7d9bb076bf63a4b25399dece", "sha256_in_prefix": "112f63fce78acdaaf16b44d8c27fda1b6710a1eb7d9bb076bf63a4b25399dece", "size_in_bytes": 7206}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/metadata.json", "path_type": "hardlink", "sha256": "7dd50142779110805b79534bd228bdeb83c2f23745935c01dac03538d967a497", "sha256_in_prefix": "7dd50142779110805b79534bd228bdeb83c2f23745935c01dac03538d967a497", "size_in_bytes": 195}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/nccl_sum.py", "path_type": "hardlink", "sha256": "1c7ae30eb9c94afa75b3fb3a2aed24edbb554652e9d06aa20d4e3e7534cd4613", "sha256_in_prefix": "1c7ae30eb9c94afa75b3fb3a2aed24edbb554652e9d06aa20d4e3e7534cd4613", "size_in_bytes": 4654}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nccl_sum/stats.ipynb", "path_type": "hardlink", "sha256": "d223f71b208bf2459fbab3a8eb7781aa19152684131cb4f631d2d76d398aef9f", "sha256_in_prefix": "d223f71b208bf2459fbab3a8eb7781aa19152684131cb4f631d2d76d398aef9f", "size_in_bytes": 6143}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nic_metric_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "48126505471dd4957bd59a23f822d84d8b84d4a56b84302911aa52032d43f8e3", "sha256_in_prefix": "48126505471dd4957bd59a23f822d84d8b84d4a56b84302911aa52032d43f8e3", "size_in_bytes": 3933}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nic_metric_map/metadata.json", "path_type": "hardlink", "sha256": "cd820b495545e0f3f7381e4b3fcdea495cb3b40a3bab490c7ddd2eb28d830a0c", "sha256_in_prefix": "cd820b495545e0f3f7381e4b3fcdea495cb3b40a3bab490c7ddd2eb28d830a0c", "size_in_bytes": 172}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nic_metric_map/nic_metric_map.py", "path_type": "hardlink", "sha256": "60ed132db9f8c35c9038006d7a5305da6f2bdd6f8bc9358d97f461fa43d1a575", "sha256_in_prefix": "60ed132db9f8c35c9038006d7a5305da6f2bdd6f8bc9358d97f461fa43d1a575", "size_in_bytes": 6638}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/metadata.json", "path_type": "hardlink", "sha256": "3873a590f182003f4bd0a64a1aaf88c727c98f32770a15782c8391b3c7e855a2", "sha256_in_prefix": "3873a590f182003f4bd0a64a1aaf88c727c98f32770a15782c8391b3c7e855a2", "size_in_bytes": 296}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/nvtx_gpu_proj_pace.py", "path_type": "hardlink", "sha256": "9f8354b3064bba96151448142d60eca48c2749432116b941c5e56de8f26e56f8", "sha256_in_prefix": "9f8354b3064bba96151448142d60eca48c2749432116b941c5e56de8f26e56f8", "size_in_bytes": 6153}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_pace/pace.ipynb", "path_type": "hardlink", "sha256": "128cc1b2f36b2e7fe0ca767d29bc62d82013600fc0e3095c42a52ca423778c7b", "sha256_in_prefix": "128cc1b2f36b2e7fe0ca767d29bc62d82013600fc0e3095c42a52ca423778c7b", "size_in_bytes": 11547}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/metadata.json", "path_type": "hardlink", "sha256": "58eafdfbd5e4003ad260023998e1c4ac20afc75f5565577eb5dead84e8d9dfb0", "sha256_in_prefix": "58eafdfbd5e4003ad260023998e1c4ac20afc75f5565577eb5dead84e8d9dfb0", "size_in_bytes": 289}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/nvtx_gpu_proj_sum.py", "path_type": "hardlink", "sha256": "ea60bc898d4e8a61b21c54b1a9105d409e19fd5ae1192b4123c17b4f2e0025dc", "sha256_in_prefix": "ea60bc898d4e8a61b21c54b1a9105d409e19fd5ae1192b4123c17b4f2e0025dc", "size_in_bytes": 5992}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_sum/stats.ipynb", "path_type": "hardlink", "sha256": "6a056d8681f4541b7157591e51dd9c278de1db1f3fc4c9a6fcbf105507468670", "sha256_in_prefix": "6a056d8681f4541b7157591e51dd9c278de1db1f3fc4c9a6fcbf105507468670", "size_in_bytes": 6655}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/metadata.json", "path_type": "hardlink", "sha256": "120d66a0302c21ada344ef16af3702dcccc9bb12385feb99b2cafe7c5e9e7622", "sha256_in_prefix": "120d66a0302c21ada344ef16af3702dcccc9bb12385feb99b2cafe7c5e9e7622", "size_in_bytes": 799}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/nvtx_gpu_proj_trace.py", "path_type": "hardlink", "sha256": "9ffd0c3844f960e911350540efcf47ad8317772013a28d4b5b2bc2851195a8bd", "sha256_in_prefix": "9ffd0c3844f960e911350540efcf47ad8317772013a28d4b5b2bc2851195a8bd", "size_in_bytes": 3178}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_gpu_proj_trace/trace.ipynb", "path_type": "hardlink", "sha256": "07bee088706a42808f58d165932241d1c87072054fade56616f7e2e7ba76d3be", "sha256_in_prefix": "07bee088706a42808f58d165932241d1c87072054fade56616f7e2e7ba76d3be", "size_in_bytes": 2242}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/metadata.json", "path_type": "hardlink", "sha256": "55c0e534c2d25fda04b317be19412f1d31e6fabca37e2dc74887e5908ca93786", "sha256_in_prefix": "55c0e534c2d25fda04b317be19412f1d31e6fabca37e2dc74887e5908ca93786", "size_in_bytes": 237}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/nvtx_pace.py", "path_type": "hardlink", "sha256": "8944226230b751b2c9b93eb47d34c01dd731455e0c2c4f270856b112488fa294", "sha256_in_prefix": "8944226230b751b2c9b93eb47d34c01dd731455e0c2c4f270856b112488fa294", "size_in_bytes": 4742}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_pace/pace.ipynb", "path_type": "hardlink", "sha256": "128cc1b2f36b2e7fe0ca767d29bc62d82013600fc0e3095c42a52ca423778c7b", "sha256_in_prefix": "128cc1b2f36b2e7fe0ca767d29bc62d82013600fc0e3095c42a52ca423778c7b", "size_in_bytes": 11547}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/metadata.json", "path_type": "hardlink", "sha256": "705e0f05c4f75902cc7544ffc59042e1eb3659d71de2258235c0e970aa17dc1d", "sha256_in_prefix": "705e0f05c4f75902cc7544ffc59042e1eb3659d71de2258235c0e970aa17dc1d", "size_in_bytes": 253}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/nvtx_sum.py", "path_type": "hardlink", "sha256": "37555e187225c9a20f53d6051185285b18918113477420f64f7f03a8f97ada5f", "sha256_in_prefix": "37555e187225c9a20f53d6051185285b18918113477420f64f7f03a8f97ada5f", "size_in_bytes": 4566}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/nvtx_sum/stats.ipynb", "path_type": "hardlink", "sha256": "d1f71e0817b49b0979285371cf90a64725eea73c97f68e185540661eae9fedc4", "sha256_in_prefix": "d1f71e0817b49b0979285371cf90a64725eea73c97f68e185540661eae9fedc4", "size_in_bytes": 6132}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/metadata.json", "path_type": "hardlink", "sha256": "4a1191e99a0be0117da7f77433a1078db2be14d0c264bbee07649d7ebd6011a1", "sha256_in_prefix": "4a1191e99a0be0117da7f77433a1078db2be14d0c264bbee07649d7ebd6011a1", "size_in_bytes": 206}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/osrt_sum.py", "path_type": "hardlink", "sha256": "71819013a9878435557ed23a78303b5d3e34af4dc5f59087ce55d70df75259c5", "sha256_in_prefix": "71819013a9878435557ed23a78303b5d3e34af4dc5f59087ce55d70df75259c5", "size_in_bytes": 5101}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/osrt_sum/stats.ipynb", "path_type": "hardlink", "sha256": "714eece57c5ecb923e1f97797e1e6ecdcdfd770c564ef15118164208435104ea", "sha256_in_prefix": "714eece57c5ecb923e1f97797e1e6ecdcdfd770c564ef15118164208435104ea", "size_in_bytes": 4611}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/heatmap.ipynb", "path_type": "hardlink", "sha256": "cc66d03f350465e9aaa5252f51f7d6d9bea09951a4fa07ac6b7e05b1e46d2e0c", "sha256_in_prefix": "cc66d03f350465e9aaa5252f51f7d6d9bea09951a4fa07ac6b7e05b1e46d2e0c", "size_in_bytes": 2577}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/metadata.json", "path_type": "hardlink", "sha256": "f3ce51721c1d4699d556d9add7cff07bbabaa9214240a6f321d4b41d03030cd8", "sha256_in_prefix": "f3ce51721c1d4699d556d9add7cff07bbabaa9214240a6f321d4b41d03030cd8", "size_in_bytes": 764}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/recipes/ucx_gpu_time_util_map/ucx_gpu_time_util_map.py", "path_type": "hardlink", "sha256": "7a0d76c67fc63eacac493e2554001583bacfae1aaf6f3dc987faf383ac8d1e51", "sha256_in_prefix": "7a0d76c67fc63eacac493e2554001583bacfae1aaf6f3dc987faf383ac8d1e51", "size_in_bytes": 6995}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/common.txt", "path_type": "hardlink", "sha256": "6e839527145b9976ab48b2840a302c8f7904f7bf3fa67ae8529cdab907803843", "sha256_in_prefix": "6e839527145b9976ab48b2840a302c8f7904f7bf3fa67ae8529cdab907803843", "size_in_bytes": 32}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/dask.txt", "path_type": "hardlink", "sha256": "4e70e288f9d23ea3b8b74b5f61a5be1872e0c980e3fbeb241be3447e8a543ece", "sha256_in_prefix": "4e70e288f9d23ea3b8b74b5f61a5be1872e0c980e3fbeb241be3447e8a543ece", "size_in_bytes": 19}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/requirements/jupyter.txt", "path_type": "hardlink", "sha256": "dba7c0a26df1fa46cf673b2ffdac95be113a06b29fef21af0c5f20ab9105b267", "sha256_in_prefix": "dba7c0a26df1fa46cf673b2ffdac95be113a06b29fef21af0c5f20ab9105b267", "size_in_bytes": 32}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/_sqlite3.cpython-310-x86_64-linux-gnu.so", "path_type": "hardlink", "sha256": "c3bf6b3d53bc9beb527f6b8ccb3ea7a0a2f706b76c8b164852efd2a338f01b95", "sha256_in_prefix": "c3bf6b3d53bc9beb527f6b8ccb3ea7a0a2f706b76c8b164852efd2a338f01b95", "size_in_bytes": 110888}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/__init__.py", "path_type": "hardlink", "sha256": "3df225315effca29c26196714cf4653a554671ec877019b4bb9d2c0d3a951dd6", "sha256_in_prefix": "3df225315effca29c26196714cf4653a554671ec877019b4bb9d2c0d3a951dd6", "size_in_bytes": 2607}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/dbapi2.py", "path_type": "hardlink", "sha256": "7296221686beb47624ea7bf4ab82e9d5aa4e25160042946d2827868897762694", "sha256_in_prefix": "7296221686beb47624ea7bf4ab82e9d5aa4e25160042946d2827868897762694", "size_in_bytes": 3426}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/python/packages/nsys_recipe/third_party/sqlite3/dump.py", "path_type": "hardlink", "sha256": "30769c19582b0f62506e6bf9e4f36a86f9fd92f2e5c618f770eb14da0c05f16e", "sha256_in_prefix": "30769c19582b0f62506e6bf9e4f36a86f9fd92f2e5c618f770eb14da0c05f16e", "size_in_bytes": 3374}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/README.txt", "path_type": "hardlink", "sha256": "5cea627396f2f88286220ef392a6a7a63e30001580ecb24218215ad7048d9150", "sha256_in_prefix": "5cea627396f2f88286220ef392a6a7a63e30001580ecb24218215ad7048d9150", "size_in_bytes": 1834}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/_sql.py", "path_type": "hardlink", "sha256": "1653b8cb5fd70c32153a4656a4b5514991492dc100a713900c78a4b944f83c0e", "sha256_in_prefix": "1653b8cb5fd70c32153a4656a4b5514991492dc100a713900c78a4b944f83c0e", "size_in_bytes": 1599}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/_sqlfile.py", "path_type": "hardlink", "sha256": "39826e5801bf1bf7aea7ec8cad5dc68c014b7a76e5d025163a41d24b3851d192", "sha256_in_prefix": "39826e5801bf1bf7aea7ec8cad5dc68c014b7a76e5d025163a41d24b3851d192", "size_in_bytes": 1934}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/_tbl.py", "path_type": "hardlink", "sha256": "cb70d20b9771e3e4571f19d24fc6cba51502d5229c4e73c57d3e77f049e42e76", "sha256_in_prefix": "cb70d20b9771e3e4571f19d24fc6cba51502d5229c4e73c57d3e77f049e42e76", "size_in_bytes": 1865}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/_values.py", "path_type": "hardlink", "sha256": "722bf36c182f13e337a86a3232e602762a49eda0c5e3c24c9bea2939cdc4133a", "sha256_in_prefix": "722bf36c182f13e337a86a3232e602762a49eda0c5e3c24c9bea2939cdc4133a", "size_in_bytes": 2047}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/apigpusum.py", "path_type": "hardlink", "sha256": "9e0f6a7911ca8152e0db712468a91635b66794a03ae06876581072bb5f06687f", "sha256_in_prefix": "9e0f6a7911ca8152e0db712468a91635b66794a03ae06876581072bb5f06687f", "size_in_bytes": 4765}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_api_gpu_sum.py", "path_type": "hardlink", "sha256": "e8c62a4a9ef21cef6c3da83f0270baf2c2027cb2a274b6acb65fa3f0feec25a6", "sha256_in_prefix": "e8c62a4a9ef21cef6c3da83f0270baf2c2027cb2a274b6acb65fa3f0feec25a6", "size_in_bytes": 6609}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_api_sum.py", "path_type": "hardlink", "sha256": "052fc56781035a98eeeac20cb3f73cba1ef3940815a9177d946dd3a0373d276d", "sha256_in_prefix": "052fc56781035a98eeeac20cb3f73cba1ef3940815a9177d946dd3a0373d276d", "size_in_bytes": 3225}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_api_trace.py", "path_type": "hardlink", "sha256": "e5578274b1f84d0db6cfaf99a6c24b2dab57c8eba729b4497c61afe891749418", "sha256_in_prefix": "e5578274b1f84d0db6cfaf99a6c24b2dab57c8eba729b4497c61afe891749418", "size_in_bytes": 2555}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_kern_gb_sum.py", "path_type": "hardlink", "sha256": "c8a992a54b2fb241e5de90ed093ca0b55ff42fe224a4f81113b377db7f13981c", "sha256_in_prefix": "c8a992a54b2fb241e5de90ed093ca0b55ff42fe224a4f81113b377db7f13981c", "size_in_bytes": 4711}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_kern_sum.py", "path_type": "hardlink", "sha256": "6903af045e4344aa4d25497cd680d279d4bb57e8f6e3c1fcb49b7d0dd42437e5", "sha256_in_prefix": "6903af045e4344aa4d25497cd680d279d4bb57e8f6e3c1fcb49b7d0dd42437e5", "size_in_bytes": 4863}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_mem_size_sum.py", "path_type": "hardlink", "sha256": "5a640518d751eeec2511be3a6154ac97d9ff74f09defc386ef6e0eeb057b1ae3", "sha256_in_prefix": "5a640518d751eeec2511be3a6154ac97d9ff74f09defc386ef6e0eeb057b1ae3", "size_in_bytes": 3504}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_mem_time_sum.py", "path_type": "hardlink", "sha256": "9e66b0ce547efeb390c604c9a482280a69cbde680f32ff96e25b3a97d6d3560e", "sha256_in_prefix": "9e66b0ce547efeb390c604c9a482280a69cbde680f32ff96e25b3a97d6d3560e", "size_in_bytes": 4144}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_sum.py", "path_type": "hardlink", "sha256": "4fdb5a1558e57cbd39630b4c2459ff25d2d630ea65094c13cb5da028a729e69c", "sha256_in_prefix": "4fdb5a1558e57cbd39630b4c2459ff25d2d630ea65094c13cb5da028a729e69c", "size_in_bytes": 5987}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_gpu_trace.py", "path_type": "hardlink", "sha256": "0ffd4175167a5095c4b77a9f64a5138911b0b488736840ef6832dc40fba1c77c", "sha256_in_prefix": "0ffd4175167a5095c4b77a9f64a5138911b0b488736840ef6832dc40fba1c77c", "size_in_bytes": 9055}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_kern_exec_sum.py", "path_type": "hardlink", "sha256": "b16a9c12e4c464960294df80ed53b2e5b153e16c4460590a4626bd72178e3002", "sha256_in_prefix": "b16a9c12e4c464960294df80ed53b2e5b153e16c4460590a4626bd72178e3002", "size_in_bytes": 7154}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cuda_kern_exec_trace.py", "path_type": "hardlink", "sha256": "102734c7da9483a802dd2a1a8c72c74dfa160735113b3026dca871bfbed85a08", "sha256_in_prefix": "102734c7da9483a802dd2a1a8c72c74dfa160735113b3026dca871bfbed85a08", "size_in_bytes": 5863}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cudaapisum.py", "path_type": "hardlink", "sha256": "6f64277a26658342ba5761ef5d1285241e84b411ea2772882c355cdc9e4cb713", "sha256_in_prefix": "6f64277a26658342ba5761ef5d1285241e84b411ea2772882c355cdc9e4cb713", "size_in_bytes": 2135}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/cudaapitrace.py", "path_type": "hardlink", "sha256": "53e80acffb9ffe08ffdeaee81f4e9e5601d94d18233ec5840be25e5570921831", "sha256_in_prefix": "53e80acffb9ffe08ffdeaee81f4e9e5601d94d18233ec5840be25e5570921831", "size_in_bytes": 1847}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx11_pix_sum.py", "path_type": "hardlink", "sha256": "03b5f6d65bf9a7a4c2ded2adb1f26b8e0983a176599c68da3262513416c2e3d6", "sha256_in_prefix": "03b5f6d65bf9a7a4c2ded2adb1f26b8e0983a176599c68da3262513416c2e3d6", "size_in_bytes": 3287}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx11pixsum.py", "path_type": "hardlink", "sha256": "d04e1fc5c29a4685d721040d3e246834b03a8f6dd58b6d63cc7aa8e0c328d3e6", "sha256_in_prefix": "d04e1fc5c29a4685d721040d3e246834b03a8f6dd58b6d63cc7aa8e0c328d3e6", "size_in_bytes": 2352}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx12_gpu_marker_sum.py", "path_type": "hardlink", "sha256": "9bff6fdec56726ece39894ee17f16cf580aadd169989d19078efae1c800e2c43", "sha256_in_prefix": "9bff6fdec56726ece39894ee17f16cf580aadd169989d19078efae1c800e2c43", "size_in_bytes": 3076}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx12_pix_sum.py", "path_type": "hardlink", "sha256": "fddec173410dcf042b7d66aa4440719e9d4333df7d5620ecc046b6e182ee2691", "sha256_in_prefix": "fddec173410dcf042b7d66aa4440719e9d4333df7d5620ecc046b6e182ee2691", "size_in_bytes": 3288}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx12gpumarkersum.py", "path_type": "hardlink", "sha256": "27c1a06a846f31958e718db97ce31be6eb512b0868456fc38a9f12a840832094", "sha256_in_prefix": "27c1a06a846f31958e718db97ce31be6eb512b0868456fc38a9f12a840832094", "size_in_bytes": 2115}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/dx12pixsum.py", "path_type": "hardlink", "sha256": "c07b78dcebf85399ce8486c2c8f531a47f8ebb8286d71ce1818c1e14e196e1b2", "sha256_in_prefix": "c07b78dcebf85399ce8486c2c8f531a47f8ebb8286d71ce1818c1e14e196e1b2", "size_in_bytes": 2352}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpukerngbsum.py", "path_type": "hardlink", "sha256": "8cdcb58b62a62348fa17d355d0f463e2cdc45ffd0eb3efcfd30b6e19560de85e", "sha256_in_prefix": "8cdcb58b62a62348fa17d355d0f463e2cdc45ffd0eb3efcfd30b6e19560de85e", "size_in_bytes": 2922}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpukernsum.py", "path_type": "hardlink", "sha256": "b3b1bba3d99d1cbe5bda89a836121b267df93f1a7b069391a68e88610011c0d6", "sha256_in_prefix": "b3b1bba3d99d1cbe5bda89a836121b267df93f1a7b069391a68e88610011c0d6", "size_in_bytes": 2671}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpumemsizesum.py", "path_type": "hardlink", "sha256": "3d310c2220fee6c4910afab7a2b92e79513e5b606fc4405440d5fdb3fa117513", "sha256_in_prefix": "3d310c2220fee6c4910afab7a2b92e79513e5b606fc4405440d5fdb3fa117513", "size_in_bytes": 2833}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpumemtimesum.py", "path_type": "hardlink", "sha256": "c525a3604d7aee3386cada37f183900e0a8c6a333e26e42eb43705c8ab343012", "sha256_in_prefix": "c525a3604d7aee3386cada37f183900e0a8c6a333e26e42eb43705c8ab343012", "size_in_bytes": 3103}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gpusum.py", "path_type": "hardlink", "sha256": "ed53e4e0c3b6df46299c7c64046d77d42ddbf7b747901460c8f19ca3874d4c2e", "sha256_in_prefix": "ed53e4e0c3b6df46299c7c64046d77d42ddbf7b747901460c8f19ca3874d4c2e", "size_in_bytes": 4183}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/gputrace.py", "path_type": "hardlink", "sha256": "60660463c6a48c894db95a997e6918824b3203d1da75c5e04fcf2dd28d60aecc", "sha256_in_prefix": "60660463c6a48c894db95a997e6918824b3203d1da75c5e04fcf2dd28d60aecc", "size_in_bytes": 6766}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/kernexecsum.py", "path_type": "hardlink", "sha256": "d249278b42d0882752165dc34e6e2b8cd261e37a145d20d16b98677f9fc1083c", "sha256_in_prefix": "d249278b42d0882752165dc34e6e2b8cd261e37a145d20d16b98677f9fc1083c", "size_in_bytes": 3972}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/kernexectrace.py", "path_type": "hardlink", "sha256": "34d9c562b2f69053323cfe1f49bdcd1185e21d70e97f27fd10ab42b8002a6185", "sha256_in_prefix": "34d9c562b2f69053323cfe1f49bdcd1185e21d70e97f27fd10ab42b8002a6185", "size_in_bytes": 2921}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/khrdebuggpusum.py", "path_type": "hardlink", "sha256": "3d196c285e9fd42d71a2cd8922b7ac00635cf49c18025b2dff8853bebf41747a", "sha256_in_prefix": "3d196c285e9fd42d71a2cd8922b7ac00635cf49c18025b2dff8853bebf41747a", "size_in_bytes": 2612}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/khrdebugsum.py", "path_type": "hardlink", "sha256": "a408cca360de620e6f280b4b674be6f2009fe3a95dabbd3deca93e8878404bfd", "sha256_in_prefix": "a408cca360de620e6f280b4b674be6f2009fe3a95dabbd3deca93e8878404bfd", "size_in_bytes": 2590}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/mpi_event_trace.py", "path_type": "hardlink", "sha256": "282a3f6365993696dcffad2163acba52f42a46cb53bfa0288f4eccb630cf6b93", "sha256_in_prefix": "282a3f6365993696dcffad2163acba52f42a46cb53bfa0288f4eccb630cf6b93", "size_in_bytes": 5120}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/network_congestion.py", "path_type": "hardlink", "sha256": "bb2ac8d06bd9de490080c76ac44c7ff71f76a0c3b5483ea7e9d1ffb909ba0c39", "sha256_in_prefix": "bb2ac8d06bd9de490080c76ac44c7ff71f76a0c3b5483ea7e9d1ffb909ba0c39", "size_in_bytes": 7001}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_gpu_proj_sum.py", "path_type": "hardlink", "sha256": "1719663d64995499bafe3ce653e0f8092e08b64f6af3b02c3beb9a81af51cb25", "sha256_in_prefix": "1719663d64995499bafe3ce653e0f8092e08b64f6af3b02c3beb9a81af51cb25", "size_in_bytes": 12200}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_gpu_proj_trace.py", "path_type": "hardlink", "sha256": "3df225cf8f458f48c7f0b58da35842fb83e9cf115c72ae988027fd55ce64efd1", "sha256_in_prefix": "3df225cf8f458f48c7f0b58da35842fb83e9cf115c72ae988027fd55ce64efd1", "size_in_bytes": 10731}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_kern_sum.py", "path_type": "hardlink", "sha256": "80c97255b0a08cbe2ae11db1eca2b85a44670c19cf46211988aa4defa74b7d8b", "sha256_in_prefix": "80c97255b0a08cbe2ae11db1eca2b85a44670c19cf46211988aa4defa74b7d8b", "size_in_bytes": 10001}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_pushpop_sum.py", "path_type": "hardlink", "sha256": "a56a5eb3230eb6bbb2e5aab3b3217b3cc9904ef4b1bbd825c5d8dc5d04a0dcb1", "sha256_in_prefix": "a56a5eb3230eb6bbb2e5aab3b3217b3cc9904ef4b1bbd825c5d8dc5d04a0dcb1", "size_in_bytes": 4336}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_pushpop_trace.py", "path_type": "hardlink", "sha256": "0edcc693ccf01dba925281734a0fc5f1655a7b21279caddaf3e9c4ee729204db", "sha256_in_prefix": "0edcc693ccf01dba925281734a0fc5f1655a7b21279caddaf3e9c4ee729204db", "size_in_bytes": 9534}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_startend_sum.py", "path_type": "hardlink", "sha256": "e9ac4eb272abf4944fdfbc3da451d698818d0c109fe60687f390ac589a630006", "sha256_in_prefix": "e9ac4eb272abf4944fdfbc3da451d698818d0c109fe60687f390ac589a630006", "size_in_bytes": 4346}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtx_sum.py", "path_type": "hardlink", "sha256": "ebd655330645efc24a9e193bed3d86a80c9ce3eacdb6335a974c732e9a70b756", "sha256_in_prefix": "ebd655330645efc24a9e193bed3d86a80c9ce3eacdb6335a974c732e9a70b756", "size_in_bytes": 5271}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxgpuproj.py", "path_type": "hardlink", "sha256": "28feacbeae353d31e5a00a881a9dd46f7829f459211f5c5ff3b5c1c5546f4503", "sha256_in_prefix": "28feacbeae353d31e5a00a881a9dd46f7829f459211f5c5ff3b5c1c5546f4503", "size_in_bytes": 8970}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxkernsum.py", "path_type": "hardlink", "sha256": "c8c2e4e7ecec1d6d61839b73bd79a759a9804c3dbfa80151680b8e9403dcff63", "sha256_in_prefix": "c8c2e4e7ecec1d6d61839b73bd79a759a9804c3dbfa80151680b8e9403dcff63", "size_in_bytes": 6323}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxppsum.py", "path_type": "hardlink", "sha256": "c3f7e30dcf505a360c552ff7462889063f99cf6e06c8358767c816093e15f701", "sha256_in_prefix": "c3f7e30dcf505a360c552ff7462889063f99cf6e06c8358767c816093e15f701", "size_in_bytes": 3392}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxpptrace.py", "path_type": "hardlink", "sha256": "174988362a8081f33485af0f41c0d1b89a6a3b1e0dc3beb0a5b8f0378749a986", "sha256_in_prefix": "174988362a8081f33485af0f41c0d1b89a6a3b1e0dc3beb0a5b8f0378749a986", "size_in_bytes": 8915}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxsesum.py", "path_type": "hardlink", "sha256": "f405ef3b86df6f45711fd7b5518b0a6530f8fd255c8eb530533e2c152115fb65", "sha256_in_prefix": "f405ef3b86df6f45711fd7b5518b0a6530f8fd255c8eb530533e2c152115fb65", "size_in_bytes": 3399}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxsssum.py", "path_type": "hardlink", "sha256": "56daed136be76b58c9f8f355218a51d641f358ac8deae45aed15e2877a3b20bc", "sha256_in_prefix": "56daed136be76b58c9f8f355218a51d641f358ac8deae45aed15e2877a3b20bc", "size_in_bytes": 3401}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvtxsum.py", "path_type": "hardlink", "sha256": "436a49538e9904dfae53eca0defe409c6b33aced11eb3e105f6d743c2d91179c", "sha256_in_prefix": "436a49538e9904dfae53eca0defe409c6b33aced11eb3e105f6d743c2d91179c", "size_in_bytes": 4125}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/nvvideo_api_sum.py", "path_type": "hardlink", "sha256": "3722d03a13e176289310c7cb2aeedda4d46447613294f2421558954f0156d49d", "sha256_in_prefix": "3722d03a13e176289310c7cb2aeedda4d46447613294f2421558954f0156d49d", "size_in_bytes": 3983}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/openacc_sum.py", "path_type": "hardlink", "sha256": "b1e823c6b20ad14d1866898fae49f89c38b89573121cff5d40bfb567cd4a5dc7", "sha256_in_prefix": "b1e823c6b20ad14d1866898fae49f89c38b89573121cff5d40bfb567cd4a5dc7", "size_in_bytes": 3865}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/openaccsum.py", "path_type": "hardlink", "sha256": "cc639d0eceedf193e0c5e6ffc4fd6fd19ed696d2e9f374e31cd5e67eb1fc358b", "sha256_in_prefix": "cc639d0eceedf193e0c5e6ffc4fd6fd19ed696d2e9f374e31cd5e67eb1fc358b", "size_in_bytes": 2923}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/opengl_khr_gpu_range_sum.py", "path_type": "hardlink", "sha256": "72dde4f7ec8bc274490e21f9d48faf8fdb8a2119c7b9422f998e3e1b38960cae", "sha256_in_prefix": "72dde4f7ec8bc274490e21f9d48faf8fdb8a2119c7b9422f998e3e1b38960cae", "size_in_bytes": 3575}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/opengl_khr_range_sum.py", "path_type": "hardlink", "sha256": "28e780348af520f4093688a5f42342b337011b715d5e9ec91eec4dec6de0fb70", "sha256_in_prefix": "28e780348af520f4093688a5f42342b337011b715d5e9ec91eec4dec6de0fb70", "size_in_bytes": 3553}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/openmp_sum.py", "path_type": "hardlink", "sha256": "0fe564f9851e8fd5b20e77426f66e0f9effa2193c6c6a94f41640da9419f39dd", "sha256_in_prefix": "0fe564f9851e8fd5b20e77426f66e0f9effa2193c6c6a94f41640da9419f39dd", "size_in_bytes": 3334}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/openmpevtsum.py", "path_type": "hardlink", "sha256": "7e624347716ac272d9ebfb7bdefc9b71cb6a99b8daec74944bee0a3809eae6c0", "sha256_in_prefix": "7e624347716ac272d9ebfb7bdefc9b71cb6a99b8daec74944bee0a3809eae6c0", "size_in_bytes": 2408}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/osrt_sum.py", "path_type": "hardlink", "sha256": "8855cfa7d009d3bbbb202d9f1a6f35ac6dae2bce6ee2c3f47ddb2f7dc768dc1e", "sha256_in_prefix": "8855cfa7d009d3bbbb202d9f1a6f35ac6dae2bce6ee2c3f47ddb2f7dc768dc1e", "size_in_bytes": 3133}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/osrtsum.py", "path_type": "hardlink", "sha256": "9e6d0ba7be93e22e4a5793cc9415895c22737df99b65f007ad37a8e2dce0609c", "sha256_in_prefix": "9e6d0ba7be93e22e4a5793cc9415895c22737df99b65f007ad37a8e2dce0609c", "size_in_bytes": 2035}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/um_cpu_page_faults_sum.py", "path_type": "hardlink", "sha256": "c203f37db64f9d263b9dc5b3f1e0e9e92bd02347ec420eac6a0aad6d023b29dc", "sha256_in_prefix": "c203f37db64f9d263b9dc5b3f1e0e9e92bd02347ec420eac6a0aad6d023b29dc", "size_in_bytes": 1757}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/um_sum.py", "path_type": "hardlink", "sha256": "4911f5f88c5730b166218ef121f4833122269fed17776fc1fa5d28a10b356200", "sha256_in_prefix": "4911f5f88c5730b166218ef121f4833122269fed17776fc1fa5d28a10b356200", "size_in_bytes": 5839}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/um_total_sum.py", "path_type": "hardlink", "sha256": "facc27c86991b9cc55c830c1fcb0b8740a937a34e2be469bcd40bb1914e5b3bd", "sha256_in_prefix": "facc27c86991b9cc55c830c1fcb0b8740a937a34e2be469bcd40bb1914e5b3bd", "size_in_bytes": 3889}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/umcpupagefaults.py", "path_type": "hardlink", "sha256": "32cc4d312c5a176574d1f536bf0c62cd2e69599a07afc85da46a5e1777eecc2a", "sha256_in_prefix": "32cc4d312c5a176574d1f536bf0c62cd2e69599a07afc85da46a5e1777eecc2a", "size_in_bytes": 1463}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/unifiedmemory.py", "path_type": "hardlink", "sha256": "225ce43ddc8c788d79575c89572b0ed73842a92e4828a4358e62fc88dbee3aa3", "sha256_in_prefix": "225ce43ddc8c788d79575c89572b0ed73842a92e4828a4358e62fc88dbee3aa3", "size_in_bytes": 5178}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/unifiedmemorytotals.py", "path_type": "hardlink", "sha256": "0bfa5021ba6219246cc3d1015985a1fbca85df33febdaa9829c1fcdff499a110", "sha256_in_prefix": "0bfa5021ba6219246cc3d1015985a1fbca85df33febdaa9829c1fcdff499a110", "size_in_bytes": 3302}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkan_api_sum.py", "path_type": "hardlink", "sha256": "d6fc38325cd75d1f9833ae1a017e39997a305a34e70e114c0f0c653d8b0076c2", "sha256_in_prefix": "d6fc38325cd75d1f9833ae1a017e39997a305a34e70e114c0f0c653d8b0076c2", "size_in_bytes": 3134}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkan_api_trace.py", "path_type": "hardlink", "sha256": "e893d9ae57c048d3914b6d155984f16d83830375ef7ea3c9968c878e3bb01b17", "sha256_in_prefix": "e893d9ae57c048d3914b6d155984f16d83830375ef7ea3c9968c878e3bb01b17", "size_in_bytes": 2494}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkan_gpu_marker_sum.py", "path_type": "hardlink", "sha256": "6270c878578709da89ac2cb63ac53db72c0a8a347035cac7376bcc05fdf15d7d", "sha256_in_prefix": "6270c878578709da89ac2cb63ac53db72c0a8a347035cac7376bcc05fdf15d7d", "size_in_bytes": 3538}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkan_marker_sum.py", "path_type": "hardlink", "sha256": "0de77aa0df06cfeb4d89b32f065c1d106961bf7bef5e53827058f73c6d54aa69", "sha256_in_prefix": "0de77aa0df06cfeb4d89b32f065c1d106961bf7bef5e53827058f73c6d54aa69", "size_in_bytes": 3314}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkanapisum.py", "path_type": "hardlink", "sha256": "424127d069491d8d9fc20b645cbd34374b238f242a1928c9a50e78f5ecd2e422", "sha256_in_prefix": "424127d069491d8d9fc20b645cbd34374b238f242a1928c9a50e78f5ecd2e422", "size_in_bytes": 2191}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkanapitrace.py", "path_type": "hardlink", "sha256": "ca643e9c15747b371be081e8eb88022bfc4220114a60d18d317ed6e32a852c5e", "sha256_in_prefix": "ca643e9c15747b371be081e8eb88022bfc4220114a60d18d317ed6e32a852c5e", "size_in_bytes": 1741}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkangpumarkersum.py", "path_type": "hardlink", "sha256": "83d8737c18f05c7b3a1a49b435a52958006e22214d33fcfe6731a320bba36db6", "sha256_in_prefix": "83d8737c18f05c7b3a1a49b435a52958006e22214d33fcfe6731a320bba36db6", "size_in_bytes": 2610}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/vulkanmarkerssum.py", "path_type": "hardlink", "sha256": "2cec5610899dd221f3ca3fe4e351cef4d284f3642186c0fdffa955ca4995110f", "sha256_in_prefix": "2cec5610899dd221f3ca3fe4e351cef4d284f3642186c0fdffa955ca4995110f", "size_in_bytes": 2379}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/wddm_queue_sum.py", "path_type": "hardlink", "sha256": "745152c32d748c1ad384fc79a693bab10aea7ef785322b10399118f8184801e5", "sha256_in_prefix": "745152c32d748c1ad384fc79a693bab10aea7ef785322b10399118f8184801e5", "size_in_bytes": 8048}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/reports/wddmqueuesdetails.py", "path_type": "hardlink", "sha256": "3c4d670cbccb56ecaec9397c97675ad221a64193d0de71e334884c2cc73c8669", "sha256_in_prefix": "3c4d670cbccb56ecaec9397c97675ad221a64193d0de71e334884c2cc73c8669", "size_in_bytes": 7101}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/README.txt", "path_type": "hardlink", "sha256": "5cea627396f2f88286220ef392a6a7a63e30001580ecb24218215ad7048d9150", "sha256_in_prefix": "5cea627396f2f88286220ef392a6a7a63e30001580ecb24218215ad7048d9150", "size_in_bytes": 1834}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda-async-memcpy.py", "path_type": "hardlink", "sha256": "426d9cc80a075a4649e9bda1dc117e1542215c04b147ef11fe5c51650d3491d8", "sha256_in_prefix": "426d9cc80a075a4649e9bda1dc117e1542215c04b147ef11fe5c51650d3491d8", "size_in_bytes": 3432}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda-sync-api.py", "path_type": "hardlink", "sha256": "1e89d2388192f51b894f370a2c16bd53603c10258ec5708da899973a019c2aa2", "sha256_in_prefix": "1e89d2388192f51b894f370a2c16bd53603c10258ec5708da899973a019c2aa2", "size_in_bytes": 2526}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda-sync-memcpy.py", "path_type": "hardlink", "sha256": "9d4f2a69b361f1f26cf91cec64ee565f3fa1d7b49adafe5d2fe35e330954502d", "sha256_in_prefix": "9d4f2a69b361f1f26cf91cec64ee565f3fa1d7b49adafe5d2fe35e330954502d", "size_in_bytes": 3482}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda-sync-memset.py", "path_type": "hardlink", "sha256": "57ab8af592c257678eaacb489cb4e5e543f1ca1370343f041e86bef9f98d1771", "sha256_in_prefix": "57ab8af592c257678eaacb489cb4e5e543f1ca1370343f041e86bef9f98d1771", "size_in_bytes": 3351}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda_api_sync.py", "path_type": "hardlink", "sha256": "a0977f52d71f27d6d56144401adbe1a5e061bb198364b887177cbc2ffd0af57e", "sha256_in_prefix": "a0977f52d71f27d6d56144401adbe1a5e061bb198364b887177cbc2ffd0af57e", "size_in_bytes": 4289}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda_memcpy_async.py", "path_type": "hardlink", "sha256": "d10ab4088a72fd80335e648ac1f229571f90a5d0e4562a875ea1eb507c70e344", "sha256_in_prefix": "d10ab4088a72fd80335e648ac1f229571f90a5d0e4562a875ea1eb507c70e344", "size_in_bytes": 5274}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda_memcpy_sync.py", "path_type": "hardlink", "sha256": "533a9c224cd45275a2d0a6d5927408822be401e7562887a43fca144c0069633a", "sha256_in_prefix": "533a9c224cd45275a2d0a6d5927408822be401e7562887a43fca144c0069633a", "size_in_bytes": 5440}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/cuda_memset_sync.py", "path_type": "hardlink", "sha256": "84dec71639a44ca1a46bf629daa8ab1d28b4a48a7befbb6cab011abf2f3e40f6", "sha256_in_prefix": "84dec71639a44ca1a46bf629daa8ab1d28b4a48a7befbb6cab011abf2f3e40f6", "size_in_bytes": 5141}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/dx12-mem-op.py", "path_type": "hardlink", "sha256": "4fddc0e07ba483472680da3fd259d854e7140835ffdbb7e8d389114e6f1e7a82", "sha256_in_prefix": "4fddc0e07ba483472680da3fd259d854e7140835ffdbb7e8d389114e6f1e7a82", "size_in_bytes": 3602}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/dx12_mem_ops.py", "path_type": "hardlink", "sha256": "eed7be02a1cf0aab0632731536a03483f6fa9e1fe67181a8af06c6e7f50df8d8", "sha256_in_prefix": "eed7be02a1cf0aab0632731536a03483f6fa9e1fe67181a8af06c6e7f50df8d8", "size_in_bytes": 6690}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/gpu-low-util.py", "path_type": "hardlink", "sha256": "d7aebe66dd9c5cc4a2f395d572f9db9c02962c65f3e8eb9868920e7fc65ee370", "sha256_in_prefix": "d7aebe66dd9c5cc4a2f395d572f9db9c02962c65f3e8eb9868920e7fc65ee370", "size_in_bytes": 7369}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/gpu-starv.py", "path_type": "hardlink", "sha256": "e62467226af2108c689dd521fc77c8b81bba1bd0d83d3abe69daaeb8c2b0af80", "sha256_in_prefix": "e62467226af2108c689dd521fc77c8b81bba1bd0d83d3abe69daaeb8c2b0af80", "size_in_bytes": 5289}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/gpu_gaps.py", "path_type": "hardlink", "sha256": "f898bb792901d9d8e7b9665ade5c052a701fac96b7860957168d547c02919d21", "sha256_in_prefix": "f898bb792901d9d8e7b9665ade5c052a701fac96b7860957168d547c02919d21", "size_in_bytes": 6949}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/rules/gpu_time_util.py", "path_type": "hardlink", "sha256": "78faf516641d56c993c849bb310c3945839c1485ebbd587b23346b6917dfff01", "sha256_in_prefix": "78faf516641d56c993c849bb310c3945839c1485ebbd587b23346b6917dfff01", "size_in_bytes": 10857}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/sqlite3.dll", "path_type": "hardlink", "sha256": "6cabdcea798cced10f57d7022a93135b7dbd35dd1aee6d286c4e54ede0bcd8a3", "sha256_in_prefix": "6cabdcea798cced10f57d7022a93135b7dbd35dd1aee6d286c4e54ede0bcd8a3", "size_in_bytes": 1104472}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/sqlite3.exe", "path_type": "hardlink", "sha256": "c55811164baf3331377fb1aece3905e7221f12274b64ca65dc86a5c6303ece81", "sha256_in_prefix": "c55811164baf3331377fb1aece3905e7221f12274b64ca65dc86a5c6303ece81", "size_in_bytes": 1296464}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/targetsettings.xml", "path_type": "hardlink", "sha256": "ff751e5cbfd41890e580aa92c2ac4455a58dc515bfaf159daf65791b33ebe78a", "sha256_in_prefix": "ff751e5cbfd41890e580aa92c2ac4455a58dc515bfaf159daf65791b33ebe78a", "size_in_bytes": 6120}, {"_path": "Library/nsight-compute/2024.1.1/host/target-windows-x64/vulkan-layers/VkLayer_nsight-sys_windows.json", "path_type": "hardlink", "sha256": "3d418c5e95aa2d62eab7ee4aa940c8616d6005106df5a4fffd343838318f56f6", "sha256_in_prefix": "3d418c5e95aa2d62eab7ee4aa940c8616d6005106df5a4fffd343838318f56f6", "size_in_bytes": 3428}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AgentAPI.dll", "path_type": "hardlink", "sha256": "6499fcacc94236db5c03e1a91659b80f27ec7381fe20fce4480debbadf29cae6", "sha256_in_prefix": "6499fcacc94236db5c03e1a91659b80f27ec7381fe20fce4480debbadf29cae6", "size_in_bytes": 1096776}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Analysis.dll", "path_type": "hardlink", "sha256": "9a455ea7a509b19df5ec61d33ee0b82ac9767bbc5fc5028af0f345e18b797e66", "sha256_in_prefix": "9a455ea7a509b19df5ec61d33ee0b82ac9767bbc5fc5028af0f345e18b797e66", "size_in_bytes": 22659680}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AnalysisContainersData.dll", "path_type": "hardlink", "sha256": "da0a3abb8e4287a60666b5b3b1e1e0ac63f95882367f82fd13904bee0c278e16", "sha256_in_prefix": "da0a3abb8e4287a60666b5b3b1e1e0ac63f95882367f82fd13904bee0c278e16", "size_in_bytes": 422496}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AnalysisData.dll", "path_type": "hardlink", "sha256": "aa317288d7157eddd48ddf194e96c53e5a4c164db09adb5993d89e22745b12f8", "sha256_in_prefix": "aa317288d7157eddd48ddf194e96c53e5a4c164db09adb5993d89e22745b12f8", "size_in_bytes": 4872800}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AnalysisProto.dll", "path_type": "hardlink", "sha256": "063a555bf11f7806a9853fc00492dcdfcdc2904083677fc72c6dabbf6e27b75c", "sha256_in_prefix": "063a555bf11f7806a9853fc00492dcdfcdc2904083677fc72c6dabbf6e27b75c", "size_in_bytes": 52832}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AppLib.dll", "path_type": "hardlink", "sha256": "1a7880bcf61b2000b8d4d0ffb70cd9e99d69f40161f384c91af7748d90a660d3", "sha256_in_prefix": "1a7880bcf61b2000b8d4d0ffb70cd9e99d69f40161f384c91af7748d90a660d3", "size_in_bytes": 1750088}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/AppLibInterfaces.dll", "path_type": "hardlink", "sha256": "b8fb1ccefffaededb0a86c1e98f0d69ed69c0e3a7936701e0e90637bd22c114e", "sha256_in_prefix": "b8fb1ccefffaededb0a86c1e98f0d69ed69c0e3a7936701e0e90637bd22c114e", "size_in_bytes": 132168}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Assert.dll", "path_type": "hardlink", "sha256": "9c9249ada0e57a37ef7011a61edb4ce3226a09a3391b9313d849326005058cb0", "sha256_in_prefix": "9c9249ada0e57a37ef7011a61edb4ce3226a09a3391b9313d849326005058cb0", "size_in_bytes": 214080}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/CommonProtoServices.dll", "path_type": "hardlink", "sha256": "320ff76abe35ba6bb79e80567fa92c62994c186c66029f8d4e14c2e7608a0b5e", "sha256_in_prefix": "320ff76abe35ba6bb79e80567fa92c62994c186c66029f8d4e14c2e7608a0b5e", "size_in_bytes": 3234368}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/CommonProtoStreamSections.dll", "path_type": "hardlink", "sha256": "6e8ed67c4fc4f1ff1cd2e43b3f6fca030f4976cab4973c383ea5d066cfeb0a6d", "sha256_in_prefix": "6e8ed67c4fc4f1ff1cd2e43b3f6fca030f4976cab4973c383ea5d066cfeb0a6d", "size_in_bytes": 72256}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Core.dll", "path_type": "hardlink", "sha256": "5d8a8c1bda99a9277ae83fcb2e56235b96cd419174cc392b39dea4838c95e324", "sha256_in_prefix": "5d8a8c1bda99a9277ae83fcb2e56235b96cd419174cc392b39dea4838c95e324", "size_in_bytes": 766544}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/CrashReporter.exe", "path_type": "hardlink", "sha256": "81ceb7b193f930c2ac856d1fedc93d09f739e3d82b2460db36f3881c98ff50ad", "sha256_in_prefix": "81ceb7b193f930c2ac856d1fedc93d09f739e3d82b2460db36f3881c98ff50ad", "size_in_bytes": 1123416}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/CudaDrvApiWrapper.dll", "path_type": "hardlink", "sha256": "72f800bbfa9cab94020151a7c4bdb3518ad9e035164361cb0f3ea7194fead116", "sha256_in_prefix": "72f800bbfa9cab94020151a7c4bdb3518ad9e035164361cb0f3ea7194fead116", "size_in_bytes": 256616}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/DeviceProperty.dll", "path_type": "hardlink", "sha256": "2e1885b048fbbd0e308408184364b42c442d848afeba53770e3a7cffe26159a5", "sha256_in_prefix": "2e1885b048fbbd0e308408184364b42c442d848afeba53770e3a7cffe26159a5", "size_in_bytes": 155728}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/DevicePropertyProto.dll", "path_type": "hardlink", "sha256": "00f050100c11474a73e1e3932fd11a76481590d5e347312b51d80d0a5fea0be2", "sha256_in_prefix": "00f050100c11474a73e1e3932fd11a76481590d5e347312b51d80d0a5fea0be2", "size_in_bytes": 129104}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ETWEventsHandlers.dll", "path_type": "hardlink", "sha256": "57f9c70aa8cd4b26a49a74b791122448db45dd40486233e4daa70450d24bea30", "sha256_in_prefix": "57f9c70aa8cd4b26a49a74b791122448db45dd40486233e4daa70450d24bea30", "size_in_bytes": 1143896}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/EventSource.dll", "path_type": "hardlink", "sha256": "96c5c45d2e43bc7d6be1dd5eb42e354756b7e70023c525c985aef71a004edd8f", "sha256_in_prefix": "96c5c45d2e43bc7d6be1dd5eb42e354756b7e70023c525c985aef71a004edd8f", "size_in_bytes": 112704}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/EventsView.dll", "path_type": "hardlink", "sha256": "568c27fd73f70844d99cbe7fd23f77da577fe6dfe4a5220d3077e58c0d5b4801", "sha256_in_prefix": "568c27fd73f70844d99cbe7fd23f77da577fe6dfe4a5220d3077e58c0d5b4801", "size_in_bytes": 315480}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ExternalIntegration.xml", "path_type": "hardlink", "sha256": "6af4c763cfa1156f8d007ef10db5830140ad20a353fa2d8f3bcf8709993c212c", "sha256_in_prefix": "6af4c763cfa1156f8d007ef10db5830140ad20a353fa2d8f3bcf8709993c212c", "size_in_bytes": 1477}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/GenericHierarchy.dll", "path_type": "hardlink", "sha256": "b32b6389db593928278670d3653c992bb591a4fb4231dbae772be1a42c78b9a2", "sha256_in_prefix": "b32b6389db593928278670d3653c992bb591a4fb4231dbae772be1a42c78b9a2", "size_in_bytes": 1028696}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/GpuInfo.dll", "path_type": "hardlink", "sha256": "20d5eded2b169dadce03787275ee0d4ce930f52ad12505673f6368857d08deb8", "sha256_in_prefix": "20d5eded2b169dadce03787275ee0d4ce930f52ad12505673f6368857d08deb8", "size_in_bytes": 783440}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/GpuTraits.dll", "path_type": "hardlink", "sha256": "890a439a3da6dfed2d6ed787be080c291a2d5a6aa33423ba132c44373bb89a1c", "sha256_in_prefix": "890a439a3da6dfed2d6ed787be080c291a2d5a6aa33423ba132c44373bb89a1c", "size_in_bytes": 195152}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/HostCommon.dll", "path_type": "hardlink", "sha256": "c4bc7356a92cece0f263bbf4835a6a6c86392b5c41883120ced4e204d21181bf", "sha256_in_prefix": "c4bc7356a92cece0f263bbf4835a6a6c86392b5c41883120ced4e204d21181bf", "size_in_bytes": 20576}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InjectionCommunicator.dll", "path_type": "hardlink", "sha256": "3d84754db1398c46683c1fa2b5986cb78b2c868c5aafefaf37ac25e4c9f5aa5f", "sha256_in_prefix": "3d84754db1398c46683c1fa2b5986cb78b2c868c5aafefaf37ac25e4c9f5aa5f", "size_in_bytes": 296536}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceData.dll", "path_type": "hardlink", "sha256": "6ad8eba23904de70ab585c82b0d434d1fbd5f3ce71ceacbbd7f70fb2118e1f77", "sha256_in_prefix": "6ad8eba23904de70ab585c82b0d434d1fbd5f3ce71ceacbbd7f70fb2118e1f77", "size_in_bytes": 768088}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceShared.dll", "path_type": "hardlink", "sha256": "40ce13ce1ee7f8a0d54b8e3719b638d273bb38aab5166118562c99a62e1467c6", "sha256_in_prefix": "40ce13ce1ee7f8a0d54b8e3719b638d273bb38aab5166118562c99a62e1467c6", "size_in_bytes": 453208}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceSharedBase.dll", "path_type": "hardlink", "sha256": "90ae1f3b8603d6eb1a59f655aa6b61d6af2ea19164c0b3146184019fb5f2ba8a", "sha256_in_prefix": "90ae1f3b8603d6eb1a59f655aa6b61d6af2ea19164c0b3146184019fb5f2ba8a", "size_in_bytes": 118872}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceSharedCore.dll", "path_type": "hardlink", "sha256": "8d49780513a89a1a4d56c45809fe3b5767ea655a9e46d474ffdce217a7998238", "sha256_in_prefix": "8d49780513a89a1a4d56c45809fe3b5767ea655a9e46d474ffdce217a7998238", "size_in_bytes": 113752}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/InterfaceSharedLoggers.dll", "path_type": "hardlink", "sha256": "5abea40b0199015e9bec230c05130f4885043358ae59d38ec179979c1b6d21a1", "sha256_in_prefix": "5abea40b0199015e9bec230c05130f4885043358ae59d38ec179979c1b6d21a1", "size_in_bytes": 20056}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/NvLog.dll", "path_type": "hardlink", "sha256": "53065fb9b7e0d49a5be4c6810137a4ff31f3def60f74f2d3e3805198dc378a67", "sha256_in_prefix": "53065fb9b7e0d49a5be4c6810137a4ff31f3def60f74f2d3e3805198dc378a67", "size_in_bytes": 57944}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/NvQtGui.dll", "path_type": "hardlink", "sha256": "c9e641820bdb7f7bf27bc5fda72eceb5b5d9eace56ac72069e6c55d6df888a3f", "sha256_in_prefix": "c9e641820bdb7f7bf27bc5fda72eceb5b5d9eace56ac72069e6c55d6df888a3f", "size_in_bytes": 9471040}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/NvmlWrapper.dll", "path_type": "hardlink", "sha256": "da138c3816fbc2911c1481b045fcf66677c2a2d5a8ec5a521eba496ffc071598", "sha256_in_prefix": "da138c3816fbc2911c1481b045fcf66677c2a2d5a8ec5a521eba496ffc071598", "size_in_bytes": 59984}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/NvtxExtData.dll", "path_type": "hardlink", "sha256": "a8a4bd5811f7e494bc19711da23abe1687ab0be2c1d1328b8394d1aa7e0b0253", "sha256_in_prefix": "a8a4bd5811f7e494bc19711da23abe1687ab0be2c1d1328b8394d1aa7e0b0253", "size_in_bytes": 290896}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/CorePlugin/CorePlugin.dll", "path_type": "hardlink", "sha256": "ccd19cf2eeec3b3b84a592eaee0e261130ce4bdf32bf4566fda8a668f0d8f18f", "sha256_in_prefix": "ccd19cf2eeec3b3b84a592eaee0e261130ce4bdf32bf4566fda8a668f0d8f18f", "size_in_bytes": 595560}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/CorePlugin/Manifest.js", "path_type": "hardlink", "sha256": "b9ebae795fe2ffb86421ced32b6672d51b09dfd005f13c926e300d354b5194c9", "sha256_in_prefix": "b9ebae795fe2ffb86421ced32b6672d51b09dfd005f13c926e300d354b5194c9", "size_in_bytes": 27570}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/ExternalIntegrationPlugin.dll", "path_type": "hardlink", "sha256": "c2622ce06332f6362193fb81d617807a3968ac3bd9736f282c006e0ec3f49a78", "sha256_in_prefix": "c2622ce06332f6362193fb81d617807a3968ac3bd9736f282c006e0ec3f49a78", "size_in_bytes": 76888}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/LinuxPlatformPlugin.dll", "path_type": "hardlink", "sha256": "3ebc9eb027c849b921fc698dde692cd3cab60b882d77030760dc241a08df6c53", "sha256_in_prefix": "3ebc9eb027c849b921fc698dde692cd3cab60b882d77030760dc241a08df6c53", "size_in_bytes": 2007144}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/Manifest.js", "path_type": "hardlink", "sha256": "07a1a7b3edb68dadd86e1a815dcd9543407fb7a49f0ef6b8b788961e4a5bbe81", "sha256_in_prefix": "07a1a7b3edb68dadd86e1a815dcd9543407fb7a49f0ef6b8b788961e4a5bbe81", "size_in_bytes": 31033}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/QuadDPlugin.dll", "path_type": "hardlink", "sha256": "cd829f37633075db52578409bfc391be31374a6bbcbad790323a1d18bd43143c", "sha256_in_prefix": "cd829f37633075db52578409bfc391be31374a6bbcbad790323a1d18bd43143c", "size_in_bytes": 7074368}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/QuadDPlugin/default.layout", "path_type": "hardlink", "sha256": "13e3e1525ba1103a1a6ee34b613cf46417da07d283b7b7744923d9bd9ce475f8", "sha256_in_prefix": "13e3e1525ba1103a1a6ee34b613cf46417da07d283b7b7744923d9bd9ce475f8", "size_in_bytes": 991}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/RebelPlugin/RebelPlugin.dll", "path_type": "hardlink", "sha256": "99415f17305815e93424f56fcddc86cb26b2c7d27d9f710b8f5aaed738e23029", "sha256_in_prefix": "99415f17305815e93424f56fcddc86cb26b2c7d27d9f710b8f5aaed738e23029", "size_in_bytes": 61466200}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/SassDebuggerPlugin/SassDebuggerPlugin.dll", "path_type": "hardlink", "sha256": "a34401718a88aecf1bff150bb5063cd70f7753b6a8e2e2d407d63a286f118cca", "sha256_in_prefix": "a34401718a88aecf1bff150bb5063cd70f7753b6a8e2e2d407d63a286f118cca", "size_in_bytes": 1823848}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/TPSConnectionPlugin.dll", "path_type": "hardlink", "sha256": "736a9494f737f63fa7c2cb38ebfd1c9d6e8d83aec9fdcc7ba4bce153b280576e", "sha256_in_prefix": "736a9494f737f63fa7c2cb38ebfd1c9d6e8d83aec9fdcc7ba4bce153b280576e", "size_in_bytes": 1711704}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/TPSSystemServerPlugin.dll", "path_type": "hardlink", "sha256": "5002508db3432061bc9afeb3ad283d7b5596f22f01ea9166241b23fa09fafff0", "sha256_in_prefix": "5002508db3432061bc9afeb3ad283d7b5596f22f01ea9166241b23fa09fafff0", "size_in_bytes": 1581672}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/TimelinePlugin/Manifest.js", "path_type": "hardlink", "sha256": "3a8fa111b3c111033e5de4f8c549ec3834c080749b4068bab4d79bb7e0cd310b", "sha256_in_prefix": "3a8fa111b3c111033e5de4f8c549ec3834c080749b4068bab4d79bb7e0cd310b", "size_in_bytes": 900}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/TimelinePlugin/TimelinePlugin.dll", "path_type": "hardlink", "sha256": "ede22d2ff567c8471e4f26bfcf6beb5805eb6290dbbedcb5a49bffefd7560f03", "sha256_in_prefix": "ede22d2ff567c8471e4f26bfcf6beb5805eb6290dbbedcb5a49bffefd7560f03", "size_in_bytes": 27720}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/WindowsPlatformPlugin.dll", "path_type": "hardlink", "sha256": "ee691ddd1bdd014d70f40792b34d8932d996f24b13b61dd104a6f9b4fd72564f", "sha256_in_prefix": "ee691ddd1bdd014d70f40792b34d8932d996f24b13b61dd104a6f9b4fd72564f", "size_in_bytes": 1966680}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qgif.dll", "path_type": "hardlink", "sha256": "b5fb4c3f97f45c9c451f571c497a3451acf6783724e1f0569149be8679c3ba82", "sha256_in_prefix": "b5fb4c3f97f45c9c451f571c497a3451acf6783724e1f0569149be8679c3ba82", "size_in_bytes": 48784}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qico.dll", "path_type": "hardlink", "sha256": "8acd3c981f4059a0e40622776c98001b0a1b98d80534ccc4d4ab3eb5ea9259e5", "sha256_in_prefix": "8acd3c981f4059a0e40622776c98001b0a1b98d80534ccc4d4ab3eb5ea9259e5", "size_in_bytes": 47248}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qjpeg.dll", "path_type": "hardlink", "sha256": "3c8eb893442e20623980aed846bfbef5bed0b95a964ef7bdf427d9e36a827c97", "sha256_in_prefix": "3c8eb893442e20623980aed846bfbef5bed0b95a964ef7bdf427d9e36a827c97", "size_in_bytes": 620176}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qsvg.dll", "path_type": "hardlink", "sha256": "afc7959d919a8e829d8e995bc14a6b9a394ffe5856ba0de1906e88a19f06bec4", "sha256_in_prefix": "afc7959d919a8e829d8e995bc14a6b9a394ffe5856ba0de1906e88a19f06bec4", "size_in_bytes": 40080}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qtga.dll", "path_type": "hardlink", "sha256": "16bf04ff28c648564f5a295433c9f7de9cd55d9d6bba32bd76168f823f1e8898", "sha256_in_prefix": "16bf04ff28c648564f5a295433c9f7de9cd55d9d6bba32bd76168f823f1e8898", "size_in_bytes": 39056}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qtiff.dll", "path_type": "hardlink", "sha256": "eb7403c2786b6408e774a188a19e73ef0807316ffa30de349f859c9490bd4997", "sha256_in_prefix": "eb7403c2786b6408e774a188a19e73ef0807316ffa30de349f859c9490bd4997", "size_in_bytes": 422544}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/imageformats/qwbmp.dll", "path_type": "hardlink", "sha256": "a494a9c8f995a3b6d8b82ecc0fc62d74c484e182ab05ae9b679c251d95e8ffe4", "sha256_in_prefix": "a494a9c8f995a3b6d8b82ecc0fc62d74c484e182ab05ae9b679c251d95e8ffe4", "size_in_bytes": 37520}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/platforms/qwindows.dll", "path_type": "hardlink", "sha256": "7e273516ab2f0778b8f2f645a854f8ed5535bf712755e94e114c29cd8fa9b7c9", "sha256_in_prefix": "7e273516ab2f0778b8f2f645a854f8ed5535bf712755e94e114c29cd8fa9b7c9", "size_in_bytes": 850576}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/tls/qcertonlybackend.dll", "path_type": "hardlink", "sha256": "882fd8e18629221c0c6d2e105607cee116434ff025530da8ec9fc83ab0ca9afd", "sha256_in_prefix": "882fd8e18629221c0c6d2e105607cee116434ff025530da8ec9fc83ab0ca9afd", "size_in_bytes": 98960}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Plugins/tls/qopensslbackend.dll", "path_type": "hardlink", "sha256": "ac34cfece276d051aa879efb49d350cae71328029bd89518b38d32572557c53a", "sha256_in_prefix": "ac34cfece276d051aa879efb49d350cae71328029bd89518b38d32572557c53a", "size_in_bytes": 305808}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ProcessLauncher.dll", "path_type": "hardlink", "sha256": "e551d6da5020a35132c5184341111c212bbee13468c29d1fb722bb406d820896", "sha256_in_prefix": "e551d6da5020a35132c5184341111c212bbee13468c29d1fb722bb406d820896", "size_in_bytes": 518240}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ProtobufComm.dll", "path_type": "hardlink", "sha256": "102c11fb376c505c1b1902c62dd4f6652a04290b0888fc6e100f845f06a0d108", "sha256_in_prefix": "102c11fb376c505c1b1902c62dd4f6652a04290b0888fc6e100f845f06a0d108", "size_in_bytes": 443984}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ProtobufCommClient.dll", "path_type": "hardlink", "sha256": "edc1d88a9f97116a3c1c11ea9142f1fd7b9aee9333e2b4b6dda4d05d7f9800ac", "sha256_in_prefix": "edc1d88a9f97116a3c1c11ea9142f1fd7b9aee9333e2b4b6dda4d05d7f9800ac", "size_in_bytes": 313936}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ProtobufCommProto.dll", "path_type": "hardlink", "sha256": "fc498ff65a17953ee176f9758f2e7ec658d08467f5f127e9aa8026c6752d223d", "sha256_in_prefix": "fc498ff65a17953ee176f9758f2e7ec658d08467f5f127e9aa8026c6752d223d", "size_in_bytes": 106576}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QdstrmImporter.exe", "path_type": "hardlink", "sha256": "479b2b31cf092b82709b76cf419e2f5c9179ec60d223182472d94fd614ee7a05", "sha256_in_prefix": "479b2b31cf092b82709b76cf419e2f5c9179ec60d223182472d94fd614ee7a05", "size_in_bytes": 196184}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Charts.dll", "path_type": "hardlink", "sha256": "2a7b4fbbe6b665ceeceffd3879b55774b8e97e93f6fe8e7459ffbfde22e82128", "sha256_in_prefix": "2a7b4fbbe6b665ceeceffd3879b55774b8e97e93f6fe8e7459ffbfde22e82128", "size_in_bytes": 1669264}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Concurrent.dll", "path_type": "hardlink", "sha256": "4dae5ebb48cb85b00ce61aad9164d18ff2a57444dbc90341ef9fd8c931c3d12c", "sha256_in_prefix": "4dae5ebb48cb85b00ce61aad9164d18ff2a57444dbc90341ef9fd8c931c3d12c", "size_in_bytes": 35472}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Core.dll", "path_type": "hardlink", "sha256": "697026ff2b482df169794bd7b17a41fb47d87a518730588c668045e1f21a53da", "sha256_in_prefix": "697026ff2b482df169794bd7b17a41fb47d87a518730588c668045e1f21a53da", "size_in_bytes": 5793424}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Designer.dll", "path_type": "hardlink", "sha256": "5bbb6bd6b632800ecaf795a300f9ef22c3c294bb154009ba04147da220000860", "sha256_in_prefix": "5bbb6bd6b632800ecaf795a300f9ef22c3c294bb154009ba04147da220000860", "size_in_bytes": 5075088}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6DesignerComponents.dll", "path_type": "hardlink", "sha256": "d347004be6c96f290c408bbd91031da55916614a5c95674ecd14f5f4f8fba1ca", "sha256_in_prefix": "d347004be6c96f290c408bbd91031da55916614a5c95674ecd14f5f4f8fba1ca", "size_in_bytes": 2342544}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Gui.dll", "path_type": "hardlink", "sha256": "8b56854670882cbf1423929ffe64f22357ce2414ecfe8a6bf041efa8a2408bd1", "sha256_in_prefix": "8b56854670882cbf1423929ffe64f22357ce2414ecfe8a6bf041efa8a2408bd1", "size_in_bytes": 8071824}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Help.dll", "path_type": "hardlink", "sha256": "c4c507b61a20696e63a233caed0d8ff363931162d9c04607a9a79f2bee2829d1", "sha256_in_prefix": "c4c507b61a20696e63a233caed0d8ff363931162d9c04607a9a79f2bee2829d1", "size_in_bytes": 562832}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Multimedia.dll", "path_type": "hardlink", "sha256": "3c2386ee5cb9644a0dbb58e4825d8e63d1f3094893284487d2d3e73125ba573c", "sha256_in_prefix": "3c2386ee5cb9644a0dbb58e4825d8e63d1f3094893284487d2d3e73125ba573c", "size_in_bytes": 767632}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6MultimediaQuick.dll", "path_type": "hardlink", "sha256": "1aa5a8412b61caa20e9a6355f854323222c5a7ccdc08802b901b613e27b50bb2", "sha256_in_prefix": "1aa5a8412b61caa20e9a6355f854323222c5a7ccdc08802b901b613e27b50bb2", "size_in_bytes": 215696}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6MultimediaWidgets.dll", "path_type": "hardlink", "sha256": "2280bc79b2c6ffb8b08b7fddf0d732dbcd822a8e0992edb48b5d05865bc3fc62", "sha256_in_prefix": "2280bc79b2c6ffb8b08b7fddf0d732dbcd822a8e0992edb48b5d05865bc3fc62", "size_in_bytes": 62096}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Network.dll", "path_type": "hardlink", "sha256": "0b033bb04dd40520faec7d9e4b45a0e7a58d92c302af1d50869b4baef88dea75", "sha256_in_prefix": "0b033bb04dd40520faec7d9e4b45a0e7a58d92c302af1d50869b4baef88dea75", "size_in_bytes": 1373328}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6OpenGL.dll", "path_type": "hardlink", "sha256": "73940cb025f69cc9e05f72bd1705e7b693ac9511237462ddacaa559518ca9234", "sha256_in_prefix": "73940cb025f69cc9e05f72bd1705e7b693ac9511237462ddacaa559518ca9234", "size_in_bytes": 1925264}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6OpenGLWidgets.dll", "path_type": "hardlink", "sha256": "e6c6436cc548cd9f8ba602bf535f5cfc51aa67b5aec47203a83960e5428212ed", "sha256_in_prefix": "e6c6436cc548cd9f8ba602bf535f5cfc51aa67b5aec47203a83960e5428212ed", "size_in_bytes": 60048}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Positioning.dll", "path_type": "hardlink", "sha256": "1f811295f7aaaf95f4deaf8da23c34f42d25bed18c6f46ffc0da40616b4a71b3", "sha256_in_prefix": "1f811295f7aaaf95f4deaf8da23c34f42d25bed18c6f46ffc0da40616b4a71b3", "size_in_bytes": 480400}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6PrintSupport.dll", "path_type": "hardlink", "sha256": "65da81a5ad32cc6e64e5d206c3000923a34de850ffb1a1634ea6fac138d621f1", "sha256_in_prefix": "65da81a5ad32cc6e64e5d206c3000923a34de850ffb1a1634ea6fac138d621f1", "size_in_bytes": 393872}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Qml.dll", "path_type": "hardlink", "sha256": "b8efa7813c500766331ab52c31975e2744f645c18f2a2705cda86b13ec8bb452", "sha256_in_prefix": "b8efa7813c500766331ab52c31975e2744f645c18f2a2705cda86b13ec8bb452", "size_in_bytes": 4625040}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6QmlModels.dll", "path_type": "hardlink", "sha256": "b9154156b5d9f7deef5f54d77166a49e9b440a806895e6a9d4a8f450a0dffe28", "sha256_in_prefix": "b9154156b5d9f7deef5f54d77166a49e9b440a806895e6a9d4a8f450a0dffe28", "size_in_bytes": 684176}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Quick.dll", "path_type": "hardlink", "sha256": "93df13def1221e7b1a1b08486178544e12e2caabcc8e2e9783fd4c436d38eb85", "sha256_in_prefix": "93df13def1221e7b1a1b08486178544e12e2caabcc8e2e9783fd4c436d38eb85", "size_in_bytes": 5192336}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6QuickParticles.dll", "path_type": "hardlink", "sha256": "b004d05038d60d542883e6b007f67349b07980a6537f692e53640ed90d183c51", "sha256_in_prefix": "b004d05038d60d542883e6b007f67349b07980a6537f692e53640ed90d183c51", "size_in_bytes": 523920}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6QuickTest.dll", "path_type": "hardlink", "sha256": "811a789022842f6add92e4d1cde580083c0baaddaf4c0a0e9331bc6e5c8e9544", "sha256_in_prefix": "811a789022842f6add92e4d1cde580083c0baaddaf4c0a0e9331bc6e5c8e9544", "size_in_bytes": 304272}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6QuickWidgets.dll", "path_type": "hardlink", "sha256": "26cc6217b2ee7c16b48a38a9aee91ff3e35cc0b1952067d9c940b98bbdb8d77d", "sha256_in_prefix": "26cc6217b2ee7c16b48a38a9aee91ff3e35cc0b1952067d9c940b98bbdb8d77d", "size_in_bytes": 116368}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Sensors.dll", "path_type": "hardlink", "sha256": "bbc7dbae6f8101bda2abbb3f4f7301108265442a3f839e041ca7585e7fdcb189", "sha256_in_prefix": "bbc7dbae6f8101bda2abbb3f4f7301108265442a3f839e041ca7585e7fdcb189", "size_in_bytes": 220816}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Sql.dll", "path_type": "hardlink", "sha256": "78466223054ad3ec3badc50303f0a0873ac9f062031a3d5c2c65ceb068c2c9d4", "sha256_in_prefix": "78466223054ad3ec3badc50303f0a0873ac9f062031a3d5c2c65ceb068c2c9d4", "size_in_bytes": 279696}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6StateMachine.dll", "path_type": "hardlink", "sha256": "46989c45bedcaafce43d0cd630bee083c9ce05aee03f2b8064e311644781c1d0", "sha256_in_prefix": "46989c45bedcaafce43d0cd630bee083c9ce05aee03f2b8064e311644781c1d0", "size_in_bytes": 323728}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Svg.dll", "path_type": "hardlink", "sha256": "9834aa2cf53bef574b50dd597b283d8d83ab6825646811ce6fe9a3140d27dd6f", "sha256_in_prefix": "9834aa2cf53bef574b50dd597b283d8d83ab6825646811ce6fe9a3140d27dd6f", "size_in_bytes": 364688}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6SvgWidgets.dll", "path_type": "hardlink", "sha256": "326416668098eeaf34f486cb53643db258c88bdb79fcad5ac21155d0728398ac", "sha256_in_prefix": "326416668098eeaf34f486cb53643db258c88bdb79fcad5ac21155d0728398ac", "size_in_bytes": 56464}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Test.dll", "path_type": "hardlink", "sha256": "973a62e9c1c2d3b4cf34192fca25560e151207bb4da1eecbcff4e5e93364e2b8", "sha256_in_prefix": "973a62e9c1c2d3b4cf34192fca25560e151207bb4da1eecbcff4e5e93364e2b8", "size_in_bytes": 315536}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6UiTools.dll", "path_type": "hardlink", "sha256": "ae2b9c4512566963c63d9fb7f0c09ab440f4c2c344257a300a6a78ded2fa9a2f", "sha256_in_prefix": "ae2b9c4512566963c63d9fb7f0c09ab440f4c2c344257a300a6a78ded2fa9a2f", "size_in_bytes": 704144}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6WebChannel.dll", "path_type": "hardlink", "sha256": "203238785954b465726b5cd5caf1b4c7b4b50ebade3ea02c402eb70b853410c8", "sha256_in_prefix": "203238785954b465726b5cd5caf1b4c7b4b50ebade3ea02c402eb70b853410c8", "size_in_bytes": 247440}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6WebEngineCore.dll", "path_type": "hardlink", "sha256": "1cf655ef03d20b766c4816454145fa98c9864ced10ff9e8823bb3543fa9c89de", "sha256_in_prefix": "1cf655ef03d20b766c4816454145fa98c9864ced10ff9e8823bb3543fa9c89de", "size_in_bytes": 140047504}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6WebEngineWidgets.dll", "path_type": "hardlink", "sha256": "535b4de4c9fe941609f00a62dd428bc3afc5935f877b7dd852021152a2849d82", "sha256_in_prefix": "535b4de4c9fe941609f00a62dd428bc3afc5935f877b7dd852021152a2849d82", "size_in_bytes": 174736}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6WebSockets.dll", "path_type": "hardlink", "sha256": "b13a13c851f02d65c4f5925e0f3b443d732e587824bba38cc2fa9722f8fcdf56", "sha256_in_prefix": "b13a13c851f02d65c4f5925e0f3b443d732e587824bba38cc2fa9722f8fcdf56", "size_in_bytes": 200336}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Widgets.dll", "path_type": "hardlink", "sha256": "ddce26294eaadd430b9f25f2eaa37a2155e9947882382cc3ed148fa25322c52a", "sha256_in_prefix": "ddce26294eaadd430b9f25f2eaa37a2155e9947882382cc3ed148fa25322c52a", "size_in_bytes": 6026384}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/Qt6Xml.dll", "path_type": "hardlink", "sha256": "fbee0c3357239755cfcd764ee91db99f68a98f0bb84f9fee09c77dcb046ccf43", "sha256_in_prefix": "fbee0c3357239755cfcd764ee91db99f68a98f0bb84f9fee09c77dcb046ccf43", "size_in_bytes": 147600}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QtPropertyBrowser.dll", "path_type": "hardlink", "sha256": "44a704faf8786a5cc5ba76efbe625763732984895dd0067002000e5655456e70", "sha256_in_prefix": "44a704faf8786a5cc5ba76efbe625763732984895dd0067002000e5655456e70", "size_in_bytes": 761344}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QtWebEngineProcess.exe", "path_type": "hardlink", "sha256": "6419dcca07a3ab9256ce99539dcae63308dadec054e27c4cce1b3104d4952016", "sha256_in_prefix": "6419dcca07a3ab9256ce99539dcae63308dadec054e27c4cce1b3104d4952016", "size_in_bytes": 680080}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QuiverContainers.dll", "path_type": "hardlink", "sha256": "03566b4db3130101831ff5ac3a5c5f212f28dbbc654d8c7d20d16148a87a306e", "sha256_in_prefix": "03566b4db3130101831ff5ac3a5c5f212f28dbbc654d8c7d20d16148a87a306e", "size_in_bytes": 224856}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/QuiverEvents.dll", "path_type": "hardlink", "sha256": "c2d35af5fa7259cb7cf64ca630a2400adde38fe6175cb29304e954e86b34e035", "sha256_in_prefix": "c2d35af5fa7259cb7cf64ca630a2400adde38fe6175cb29304e954e86b34e035", "size_in_bytes": 440408}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/SshClient.dll", "path_type": "hardlink", "sha256": "703ef539b6277f3a8379090bb2f63724c6c17859b3e17480554dcefab4e1ae35", "sha256_in_prefix": "703ef539b6277f3a8379090bb2f63724c6c17859b3e17480554dcefab4e1ae35", "size_in_bytes": 776752}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/StreamSections.dll", "path_type": "hardlink", "sha256": "2f60afb17bcfeba1f222c9ae0c7b3704b4c3c1148ece30840118a4aee3b616ea", "sha256_in_prefix": "2f60afb17bcfeba1f222c9ae0c7b3704b4c3c1148ece30840118a4aee3b616ea", "size_in_bytes": 398408}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/SymbolAnalyzerLight.dll", "path_type": "hardlink", "sha256": "df33bdb4ae838486986338afaabfe9690dad47103c63e36961d3c6616e0670ea", "sha256_in_prefix": "df33bdb4ae838486986338afaabfe9690dad47103c63e36961d3c6616e0670ea", "size_in_bytes": 203840}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/SymbolDemangler.dll", "path_type": "hardlink", "sha256": "765490b95bfa81189471eb6e82b1ebcd9bb01e8dc1dcc032a14cf70c5d4df2ab", "sha256_in_prefix": "765490b95bfa81189471eb6e82b1ebcd9bb01e8dc1dcc032a14cf70c5d4df2ab", "size_in_bytes": 283736}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TelemetryQuadDClient.dll", "path_type": "hardlink", "sha256": "63df94fc484ddef71bcc7f5d304eea3c473da8eeeaa8169f4ede8e6c424fc36c", "sha256_in_prefix": "63df94fc484ddef71bcc7f5d304eea3c473da8eeeaa8169f4ede8e6c424fc36c", "size_in_bytes": 108616}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TimelineAssert.dll", "path_type": "hardlink", "sha256": "7f5ee8deff56b4bfcaa3e1e1550aaf021fb67b3a7d482e96d810c2ee0f961c4c", "sha256_in_prefix": "7f5ee8deff56b4bfcaa3e1e1550aaf021fb67b3a7d482e96d810c2ee0f961c4c", "size_in_bytes": 19528}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TimelineCommon.dll", "path_type": "hardlink", "sha256": "6e2f7222c43cfe871be05e361bdfdd98aae67e009ea18731abd21b4794adf688", "sha256_in_prefix": "6e2f7222c43cfe871be05e361bdfdd98aae67e009ea18731abd21b4794adf688", "size_in_bytes": 92760}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TimelineUIUtils.dll", "path_type": "hardlink", "sha256": "a0bfa2e9baaccc2bc31f0c3daf0b1f6f30df6f9ca7da6487ae322ac826fff169", "sha256_in_prefix": "a0bfa2e9baaccc2bc31f0c3daf0b1f6f30df6f9ca7da6487ae322ac826fff169", "size_in_bytes": 211528}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/TimelineWidget.dll", "path_type": "hardlink", "sha256": "5027561256e90c981c1e9d045a96da92d855b2c7e790aef9c84d83cb90c8514a", "sha256_in_prefix": "5027561256e90c981c1e9d045a96da92d855b2c7e790aef9c84d83cb90c8514a", "size_in_bytes": 1474136}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-file-l1-2-0.dll", "path_type": "hardlink", "sha256": "bd9e07bbc62ce82dbc30c23069a17fbfa17f1c26a9c19e50fe754d494e6cd0b1", "sha256_in_prefix": "bd9e07bbc62ce82dbc30c23069a17fbfa17f1c26a9c19e50fe754d494e6cd0b1", "size_in_bytes": 18624}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-file-l2-1-0.dll", "path_type": "hardlink", "sha256": "355633a84db0816ab6a340a086fb41c65854c313bd08d427a17389c42a1e5b69", "sha256_in_prefix": "355633a84db0816ab6a340a086fb41c65854c313bd08d427a17389c42a1e5b69", "size_in_bytes": 18624}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-localization-l1-2-0.dll", "path_type": "hardlink", "sha256": "b39a515b9e48fc6589703d45e14dcea2273a02d7fa6f2e1d17985c0228d32564", "sha256_in_prefix": "b39a515b9e48fc6589703d45e14dcea2273a02d7fa6f2e1d17985c0228d32564", "size_in_bytes": 21184}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-processthreads-l1-1-1.dll", "path_type": "hardlink", "sha256": "ccb974c24ddfa7446278ca55fc8b236d0605d2caaf273db8390d1813fc70cd5b", "sha256_in_prefix": "ccb974c24ddfa7446278ca55fc8b236d0605d2caaf273db8390d1813fc70cd5b", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-synch-l1-2-0.dll", "path_type": "hardlink", "sha256": "0c5c4dfea72595fb7ae410f8fa8da983b53a83ce81aea144fa20cab613e641b7", "sha256_in_prefix": "0c5c4dfea72595fb7ae410f8fa8da983b53a83ce81aea144fa20cab613e641b7", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-core-timezone-l1-1-0.dll", "path_type": "hardlink", "sha256": "7d4252ab1b79c5801b58a08ce16efd3b30d8235733028e5823f3709bd0a98bcf", "sha256_in_prefix": "7d4252ab1b79c5801b58a08ce16efd3b30d8235733028e5823f3709bd0a98bcf", "size_in_bytes": 18624}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-convert-l1-1-0.dll", "path_type": "hardlink", "sha256": "cd5256b2fb46deaa440950e4a68466b2b0ff61f28888383094182561738d10a9", "sha256_in_prefix": "cd5256b2fb46deaa440950e4a68466b2b0ff61f28888383094182561738d10a9", "size_in_bytes": 22720}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-environment-l1-1-0.dll", "path_type": "hardlink", "sha256": "c4ed8f65c5a0dbf325482a69ab9f8cbd8c97d6120b87ce90ac4cba54ac7d377a", "sha256_in_prefix": "c4ed8f65c5a0dbf325482a69ab9f8cbd8c97d6120b87ce90ac4cba54ac7d377a", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-filesystem-l1-1-0.dll", "path_type": "hardlink", "sha256": "6c86e40c956eb6a77313fa8dd9c46579c5421fa890043f724c004a66796d37a6", "sha256_in_prefix": "6c86e40c956eb6a77313fa8dd9c46579c5421fa890043f724c004a66796d37a6", "size_in_bytes": 20672}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-heap-l1-1-0.dll", "path_type": "hardlink", "sha256": "7050043b0362c928aa63dd7800e5b123c775425eba21a5c57cbc052ebc1b0ba2", "sha256_in_prefix": "7050043b0362c928aa63dd7800e5b123c775425eba21a5c57cbc052ebc1b0ba2", "size_in_bytes": 19648}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-locale-l1-1-0.dll", "path_type": "hardlink", "sha256": "fab41a942f623590402e4150a29d0f6f918ee096dba1e8b320ade3ec286c7475", "sha256_in_prefix": "fab41a942f623590402e4150a29d0f6f918ee096dba1e8b320ade3ec286c7475", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-math-l1-1-0.dll", "path_type": "hardlink", "sha256": "9b05a43fdc185497e8c2cea3c6b9eb0d74327bd70913a298a6e8af64514190e8", "sha256_in_prefix": "9b05a43fdc185497e8c2cea3c6b9eb0d74327bd70913a298a6e8af64514190e8", "size_in_bytes": 27840}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-multibyte-l1-1-0.dll", "path_type": "hardlink", "sha256": "d7e676b9f1e162957d0549ab0b91e2cd754643490b0654bf9a86aa1e77cb3c37", "sha256_in_prefix": "d7e676b9f1e162957d0549ab0b91e2cd754643490b0654bf9a86aa1e77cb3c37", "size_in_bytes": 26816}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-runtime-l1-1-0.dll", "path_type": "hardlink", "sha256": "bf55134f17b93d8ac4d8159a952bee17cb0c925f5256aa7f747c13e5f2d00661", "sha256_in_prefix": "bf55134f17b93d8ac4d8159a952bee17cb0c925f5256aa7f747c13e5f2d00661", "size_in_bytes": 23232}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-stdio-l1-1-0.dll", "path_type": "hardlink", "sha256": "293c76a26fbc0c86dcf5906dd9d9ddc77a5609ea8c191e88bdc907c03b80a3a5", "sha256_in_prefix": "293c76a26fbc0c86dcf5906dd9d9ddc77a5609ea8c191e88bdc907c03b80a3a5", "size_in_bytes": 24768}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-string-l1-1-0.dll", "path_type": "hardlink", "sha256": "01e3c0aa24ce9f8d62753702df5d7a827c390af5e2b76d1f1a5b96c777fd1a4e", "sha256_in_prefix": "01e3c0aa24ce9f8d62753702df5d7a827c390af5e2b76d1f1a5b96c777fd1a4e", "size_in_bytes": 24768}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-time-l1-1-0.dll", "path_type": "hardlink", "sha256": "f4163cbc464a82fce47442447351265a287561c8d64ecc2f2f97f5e73bcb4347", "sha256_in_prefix": "f4163cbc464a82fce47442447351265a287561c8d64ecc2f2f97f5e73bcb4347", "size_in_bytes": 21184}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/api-ms-win-crt-utility-l1-1-0.dll", "path_type": "hardlink", "sha256": "bba068f29609630e8c6547f1e9219e11077426c4f1e4a93b712bfba11a149358", "sha256_in_prefix": "bba068f29609630e8c6547f1e9219e11077426c4f1e4a93b712bfba11a149358", "size_in_bytes": 19136}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/arrow.dll", "path_type": "hardlink", "sha256": "1f05ec337db77abbc8c11bb05a684c647f37048fe6757f5975a03979ce34b873", "sha256_in_prefix": "1f05ec337db77abbc8c11bb05a684c647f37048fe6757f5975a03979ce34b873", "size_in_bytes": 8400464}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_atomic-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "b5e05aaa899d5ef2a7a804d8d5427aa8b86f4dc33278e49ea6e193f8d6d5234d", "sha256_in_prefix": "b5e05aaa899d5ef2a7a804d8d5427aa8b86f4dc33278e49ea6e193f8d6d5234d", "size_in_bytes": 27728}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_chrono-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "b0f5a492acb8eff742a44312cfcc963dc0864ee22b4bc27227119caae2a276a3", "sha256_in_prefix": "b0f5a492acb8eff742a44312cfcc963dc0864ee22b4bc27227119caae2a276a3", "size_in_bytes": 44608}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_container-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "6f8ebade9a4c502bb49d7517058a3332ef07b0c5e830b031d4e2083df6a0e2bf", "sha256_in_prefix": "6f8ebade9a4c502bb49d7517058a3332ef07b0c5e830b031d4e2083df6a0e2bf", "size_in_bytes": 66128}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_date_time-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "548164d95a8ae7a0290f4ce77eb9de535eec8a8a308cb18ace59aed2ba124ff1", "sha256_in_prefix": "548164d95a8ae7a0290f4ce77eb9de535eec8a8a308cb18ace59aed2ba124ff1", "size_in_bytes": 19520}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_filesystem-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "096ff9928f6aeca8afad7320c98cf7ce9426fe2718cc794b8ef3d2b29ef17b37", "sha256_in_prefix": "096ff9928f6aeca8afad7320c98cf7ce9426fe2718cc794b8ef3d2b29ef17b37", "size_in_bytes": 141888}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_iostreams-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "b1c8fede79b74fee017dd930e014ef390441212a91a0a27658fcaf46db165464", "sha256_in_prefix": "b1c8fede79b74fee017dd930e014ef390441212a91a0a27658fcaf46db165464", "size_in_bytes": 119376}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_locale-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "6286b3c08502cc2cd02c231fd5459905589aceb3074cc029d889261891afa581", "sha256_in_prefix": "6286b3c08502cc2cd02c231fd5459905589aceb3074cc029d889261891afa581", "size_in_bytes": 485440}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_program_options-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "42605a42ff593d1fd249ec905043c5b9b7e51e79559b8d689b9035f5644cfe11", "sha256_in_prefix": "42605a42ff593d1fd249ec905043c5b9b7e51e79559b8d689b9035f5644cfe11", "size_in_bytes": 362576}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_python310-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "767b20c44903b05978b55fc72fdacd08ff04d972503a3f53502d67cb02aff15d", "sha256_in_prefix": "767b20c44903b05978b55fc72fdacd08ff04d972503a3f53502d67cb02aff15d", "size_in_bytes": 202816}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_regex-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "49c8e237a3abcb526140e7f33cac49aa5a45a4d51bd454465a50ba2c90d1e697", "sha256_in_prefix": "49c8e237a3abcb526140e7f33cac49aa5a45a4d51bd454465a50ba2c90d1e697", "size_in_bytes": 244800}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_serialization-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "f9f4d7a77767071a94feb3fd994d7279bbe05d0f65af41376f49c3ec72da8020", "sha256_in_prefix": "f9f4d7a77767071a94feb3fd994d7279bbe05d0f65af41376f49c3ec72da8020", "size_in_bytes": 218688}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_system-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "8d8200e66c40c4264721487042987b287742e5aea3442d45a31f5b2128d9e873", "sha256_in_prefix": "8d8200e66c40c4264721487042987b287742e5aea3442d45a31f5b2128d9e873", "size_in_bytes": 19520}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_thread-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "139531a24850a7a00dd2a7a57bcd8a97a71f6ef806c3f590bc4f3f09e7892cbe", "sha256_in_prefix": "139531a24850a7a00dd2a7a57bcd8a97a71f6ef806c3f590bc4f3f09e7892cbe", "size_in_bytes": 86080}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/boost_timer-vc141-mt-x64-1_78.dll", "path_type": "hardlink", "sha256": "1c7835146d1701d72474cd4d73926b7a5262d0b164817be64f6b11203ea81741", "sha256_in_prefix": "1c7835146d1701d72474cd4d73926b7a5262d0b164817be64f6b11203ea81741", "size_in_bytes": 41536}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/exporter.dll", "path_type": "hardlink", "sha256": "8dad045e85835ae577ddedf46294a8b60d5d879d2e6adb0f0bd7e6c26b08aa1b", "sha256_in_prefix": "8dad045e85835ae577ddedf46294a8b60d5d879d2e6adb0f0bd7e6c26b08aa1b", "size_in_bytes": 17138776}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/libcrypto-3-x64.dll", "path_type": "hardlink", "sha256": "d1c8cfe25d4a5f59cae57d915712bace4403422abdc7e9560d5051f0e1cd0499", "sha256_in_prefix": "d1c8cfe25d4a5f59cae57d915712bace4403422abdc7e9560d5051f0e1cd0499", "size_in_bytes": 4566600}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/libssl-3-x64.dll", "path_type": "hardlink", "sha256": "4525d4406e5afac09b0d2b7760b9dddc252eabdc415bf9611fe6a5b572c74fae", "sha256_in_prefix": "4525d4406e5afac09b0d2b7760b9dddc252eabdc415bf9611fe6a5b572c74fae", "size_in_bytes": 786504}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/msdia140.dll", "path_type": "hardlink", "sha256": "ba67e8b5f387843191ace001ce2ca2ab279458be5e5967e964af2ae62eaaadca", "sha256_in_prefix": "ba67e8b5f387843191ace001ce2ca2ab279458be5e5967e964af2ae62eaaadca", "size_in_bytes": 1870736}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ncu-ui.exe", "path_type": "hardlink", "sha256": "6575c57332beb6ef78c1f044447d62b2f16a0af33fb31648bf1e764a951dce05", "sha256_in_prefix": "6575c57332beb6ef78c1f044447d62b2f16a0af33fb31648bf1e764a951dce05", "size_in_bytes": 1020488}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/nvsym.dll", "path_type": "hardlink", "sha256": "81bb1cf5cddca73fd22abfde27b599f36b17cfb12090f9dbf9d6e77aaadd4aaf", "sha256_in_prefix": "81bb1cf5cddca73fd22abfde27b599f36b17cfb12090f9dbf9d6e77aaadd4aaf", "size_in_bytes": 3770936}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/opengl32sw.dll", "path_type": "hardlink", "sha256": "8d605dab9cd6bee4e1ae18478cf9012351a1c64f573980c811e3e0de49f95141", "sha256_in_prefix": "8d605dab9cd6bee4e1ae18478cf9012351a1c64f573980c811e3e0de49f95141", "size_in_bytes": 33130568}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/parquet.dll", "path_type": "hardlink", "sha256": "8a633c4f5b22bf600d209283e2ea78077bdd604e95954665907e6cbd46ff7482", "sha256_in_prefix": "8a633c4f5b22bf600d209283e2ea78077bdd604e95954665907e6cbd46ff7482", "size_in_bytes": 1737296}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/protobuf-shared.dll", "path_type": "hardlink", "sha256": "1d4a625d571dd940d48a046bdf6deb7ef8d6cbfba8c87d577188ce70be3358df", "sha256_in_prefix": "1d4a625d571dd940d48a046bdf6deb7ef8d6cbfba8c87d577188ce70be3358df", "size_in_bytes": 2529856}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/qt.conf", "path_type": "hardlink", "sha256": "6d421d82aa08563ad1a26d44883c58512127693c42feb387645111358323ff06", "sha256_in_prefix": "6d421d82aa08563ad1a26d44883c58512127693c42feb387645111358323ff06", "size_in_bytes": 19}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/icudtl.dat", "path_type": "hardlink", "sha256": "97b978a19edd4746e9a44d9a44bb4bc519e127a203c247837ec0922f573449e3", "sha256_in_prefix": "97b978a19edd4746e9a44d9a44bb4bc519e127a203c247837ec0922f573449e3", "size_in_bytes": 10462432}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_devtools_resources.pak", "path_type": "hardlink", "sha256": "cedf0456f1e8a515e6b162a96822cef5f42bafe963ea39e3c252ca7dbe32de16", "sha256_in_prefix": "cedf0456f1e8a515e6b162a96822cef5f42bafe963ea39e3c252ca7dbe32de16", "size_in_bytes": 8556411}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources.pak", "path_type": "hardlink", "sha256": "744fce0b368efd3e48c68d1bda6ea0b68cf2bb801731da8670e34ec46c44ddc5", "sha256_in_prefix": "744fce0b368efd3e48c68d1bda6ea0b68cf2bb801731da8670e34ec46c44ddc5", "size_in_bytes": 2249706}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources_100p.pak", "path_type": "hardlink", "sha256": "7ce707889ebd5a5ffb36f94d424c44fd8c29bf14558078c60ab55fee823db5bf", "sha256_in_prefix": "7ce707889ebd5a5ffb36f94d424c44fd8c29bf14558078c60ab55fee823db5bf", "size_in_bytes": 150480}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/resources/qtwebengine_resources_200p.pak", "path_type": "hardlink", "sha256": "0db69975206faf39463e50092b9ff4b1940addfd2b5013bf6a5f1c02c47970e9", "sha256_in_prefix": "0db69975206faf39463e50092b9ff4b1940addfd2b5013bf6a5f1c02c47970e9", "size_in_bytes": 195899}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/sqlite3.dll", "path_type": "hardlink", "sha256": "1d1eb24682549420be8b6cad69aafb06e8b1d733ff68e321764aa58ae3114963", "sha256_in_prefix": "1d1eb24682549420be8b6cad69aafb06e8b1d733ff68e321764aa58ae3114963", "size_in_bytes": 1094656}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ssh.dll", "path_type": "hardlink", "sha256": "48fda400d630872c4949be2a9ae8775a5192b0d424dcf37ca259168bf815e9c0", "sha256_in_prefix": "48fda400d630872c4949be2a9ae8775a5192b0d424dcf37ca259168bf815e9c0", "size_in_bytes": 375368}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/am.pak", "path_type": "hardlink", "sha256": "6a8f3f615666f62ca649c8a3065b4976210878ab883c9428470506257d1932e2", "sha256_in_prefix": "6a8f3f615666f62ca649c8a3065b4976210878ab883c9428470506257d1932e2", "size_in_bytes": 596017}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ar.pak", "path_type": "hardlink", "sha256": "05fff35e614aefb123cda077e07da7eef393276ad24e458d9535077a5bcb7e0a", "sha256_in_prefix": "05fff35e614aefb123cda077e07da7eef393276ad24e458d9535077a5bcb7e0a", "size_in_bytes": 647482}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/bg.pak", "path_type": "hardlink", "sha256": "1f5dfb99a7ec1bd1bb5eedaafe56d16a21b255b255fbd5fd13207a0252bcaf0f", "sha256_in_prefix": "1f5dfb99a7ec1bd1bb5eedaafe56d16a21b255b255fbd5fd13207a0252bcaf0f", "size_in_bytes": 680475}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/bn.pak", "path_type": "hardlink", "sha256": "38f0d7f1212cf3cf9f8ab867c170ebfd95e1cd5b3e86b3d9bdf0e2b8919774fc", "sha256_in_prefix": "38f0d7f1212cf3cf9f8ab867c170ebfd95e1cd5b3e86b3d9bdf0e2b8919774fc", "size_in_bytes": 879799}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ca.pak", "path_type": "hardlink", "sha256": "24ad36820724dc9418c0a15fbef121fb1ffb9dfc33b091221e5308ad411881da", "sha256_in_prefix": "24ad36820724dc9418c0a15fbef121fb1ffb9dfc33b091221e5308ad411881da", "size_in_bytes": 414778}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/cs.pak", "path_type": "hardlink", "sha256": "d22fdedeacc082c0171d80d8d0eb28b988a91728042aca5d8b96f576520f0272", "sha256_in_prefix": "d22fdedeacc082c0171d80d8d0eb28b988a91728042aca5d8b96f576520f0272", "size_in_bytes": 425771}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/da.pak", "path_type": "hardlink", "sha256": "a8f30a6b261be2ea64f605a383daff89bdcfc2b64c4f1f5db1d0995268905a83", "sha256_in_prefix": "a8f30a6b261be2ea64f605a383daff89bdcfc2b64c4f1f5db1d0995268905a83", "size_in_bytes": 383702}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/de.pak", "path_type": "hardlink", "sha256": "0d8cc868e47afa5e3d57331bc7b265cddc7ea22d0545be36d31577b80018e039", "sha256_in_prefix": "0d8cc868e47afa5e3d57331bc7b265cddc7ea22d0545be36d31577b80018e039", "size_in_bytes": 411763}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/el.pak", "path_type": "hardlink", "sha256": "78c3e12131e7bf1d250e82818cdf63a7ec20b19318721ec0094e196d51cffca0", "sha256_in_prefix": "78c3e12131e7bf1d250e82818cdf63a7ec20b19318721ec0094e196d51cffca0", "size_in_bytes": 745695}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/en-GB.pak", "path_type": "hardlink", "sha256": "525d8891171748f7fbdc2b13f3855996822843ae612aa5dc605f8c4d852e48b5", "sha256_in_prefix": "525d8891171748f7fbdc2b13f3855996822843ae612aa5dc605f8c4d852e48b5", "size_in_bytes": 335437}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/en-US.pak", "path_type": "hardlink", "sha256": "a5562d5a164c42145552a7032c28e8111fd1bfc3217a4f13ba5beb943b44ef70", "sha256_in_prefix": "a5562d5a164c42145552a7032c28e8111fd1bfc3217a4f13ba5beb943b44ef70", "size_in_bytes": 338886}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/es-419.pak", "path_type": "hardlink", "sha256": "6b8c20e33c4e304df8c2ad1059c9d838b7e4750a6e8455fccc131ebe392f2576", "sha256_in_prefix": "6b8c20e33c4e304df8c2ad1059c9d838b7e4750a6e8455fccc131ebe392f2576", "size_in_bytes": 409342}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/es.pak", "path_type": "hardlink", "sha256": "f4531cb0c7801f55f3af4441215bcd0fdd56a80328b5e5f797988cf2679bd255", "sha256_in_prefix": "f4531cb0c7801f55f3af4441215bcd0fdd56a80328b5e5f797988cf2679bd255", "size_in_bytes": 409761}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/et.pak", "path_type": "hardlink", "sha256": "2826151aaa0fc43c68ebf2ca313fc98e9a4ae981cc41371579850987f58a71cc", "sha256_in_prefix": "2826151aaa0fc43c68ebf2ca313fc98e9a4ae981cc41371579850987f58a71cc", "size_in_bytes": 369332}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fa.pak", "path_type": "hardlink", "sha256": "52d18d86c6680ccc4fb8c295ffde209b3e7a44f1f21868e8e87a415753ece926", "sha256_in_prefix": "52d18d86c6680ccc4fb8c295ffde209b3e7a44f1f21868e8e87a415753ece926", "size_in_bytes": 604068}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fi.pak", "path_type": "hardlink", "sha256": "f3b851c46405a265f8fd1fe924b13ccabcd475ede634badf911a306bfcbdf6a5", "sha256_in_prefix": "f3b851c46405a265f8fd1fe924b13ccabcd475ede634badf911a306bfcbdf6a5", "size_in_bytes": 379896}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fil.pak", "path_type": "hardlink", "sha256": "2e824aad45c361e821bee269ecf3ee8c0aa8eb3558eefb630d1987494625ff6b", "sha256_in_prefix": "2e824aad45c361e821bee269ecf3ee8c0aa8eb3558eefb630d1987494625ff6b", "size_in_bytes": 427284}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/fr.pak", "path_type": "hardlink", "sha256": "7a3f2405377eb78415bfcaf167924ef8d478de4b733f6c34db287da0dbb02a03", "sha256_in_prefix": "7a3f2405377eb78415bfcaf167924ef8d478de4b733f6c34db287da0dbb02a03", "size_in_bytes": 443399}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/gu.pak", "path_type": "hardlink", "sha256": "1950f4b1c510d61e0c3f6ac94f98e29b66481b1352288daab85aa269c0b6f0df", "sha256_in_prefix": "1950f4b1c510d61e0c3f6ac94f98e29b66481b1352288daab85aa269c0b6f0df", "size_in_bytes": 853490}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/he.pak", "path_type": "hardlink", "sha256": "c323887f3feb1afbdeb47e34ebac9fa1e68fb8351fddd447876555dee0b02720", "sha256_in_prefix": "c323887f3feb1afbdeb47e34ebac9fa1e68fb8351fddd447876555dee0b02720", "size_in_bytes": 527330}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hi.pak", "path_type": "hardlink", "sha256": "5f4843d5eb7144e41a307ce633fd4972d51d349847d5abbfdcdbe79bb76f9264", "sha256_in_prefix": "5f4843d5eb7144e41a307ce633fd4972d51d349847d5abbfdcdbe79bb76f9264", "size_in_bytes": 894359}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hr.pak", "path_type": "hardlink", "sha256": "68bd9c9d60dabf2b6b82a26b6d0f4539487a6d63f798347b801fad595c59759c", "sha256_in_prefix": "68bd9c9d60dabf2b6b82a26b6d0f4539487a6d63f798347b801fad595c59759c", "size_in_bytes": 411471}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/hu.pak", "path_type": "hardlink", "sha256": "6f55c57520398995cf6c642fec97546def69350b68ba76cb7108a134538abb1a", "sha256_in_prefix": "6f55c57520398995cf6c642fec97546def69350b68ba76cb7108a134538abb1a", "size_in_bytes": 443847}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/id.pak", "path_type": "hardlink", "sha256": "7d5d2ce17f6fa4923118ce7d508351214967245d639c057ed69930c131639bf5", "sha256_in_prefix": "7d5d2ce17f6fa4923118ce7d508351214967245d639c057ed69930c131639bf5", "size_in_bytes": 363382}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/it.pak", "path_type": "hardlink", "sha256": "e21df06a78032d07f29ec42ef88dac389ad6261f61c142f6f094c5b89fab922b", "sha256_in_prefix": "e21df06a78032d07f29ec42ef88dac389ad6261f61c142f6f094c5b89fab922b", "size_in_bytes": 403465}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ja.pak", "path_type": "hardlink", "sha256": "f5f0b3c4c4a131f4af9ba8ebe2e43eaf23b86b51f296989159be25d59acb2362", "sha256_in_prefix": "f5f0b3c4c4a131f4af9ba8ebe2e43eaf23b86b51f296989159be25d59acb2362", "size_in_bytes": 493920}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/kn.pak", "path_type": "hardlink", "sha256": "8ce8ff44aab563005d8954ce5ac3b894a609e0ea712608006a3304a797279021", "sha256_in_prefix": "8ce8ff44aab563005d8954ce5ac3b894a609e0ea712608006a3304a797279021", "size_in_bytes": 985600}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ko.pak", "path_type": "hardlink", "sha256": "7175c12530eedc78f68d989fa8e10522faf52d4f0fa046eadf61296c1f81a485", "sha256_in_prefix": "7175c12530eedc78f68d989fa8e10522faf52d4f0fa046eadf61296c1f81a485", "size_in_bytes": 416397}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/lt.pak", "path_type": "hardlink", "sha256": "bbf746c5cbf0313eb7a1e0550e7554e4a438b33d79003575d5b6e518509668c0", "sha256_in_prefix": "bbf746c5cbf0313eb7a1e0550e7554e4a438b33d79003575d5b6e518509668c0", "size_in_bytes": 445742}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/lv.pak", "path_type": "hardlink", "sha256": "e06f2a173a6780e72999fc899cdfce65b78db2b8561409db4848661c432ec3de", "sha256_in_prefix": "e06f2a173a6780e72999fc899cdfce65b78db2b8561409db4848661c432ec3de", "size_in_bytes": 443433}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ml.pak", "path_type": "hardlink", "sha256": "0f4ea9c90057d27e68b66e9999412a35901929a78084cfd1f98edf4f4dd82fa6", "sha256_in_prefix": "0f4ea9c90057d27e68b66e9999412a35901929a78084cfd1f98edf4f4dd82fa6", "size_in_bytes": 1026095}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/mr.pak", "path_type": "hardlink", "sha256": "65972ccab063bd67bf3286adef48e06a591be294a8b874891ca525fb70f9068c", "sha256_in_prefix": "65972ccab063bd67bf3286adef48e06a591be294a8b874891ca525fb70f9068c", "size_in_bytes": 838025}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ms.pak", "path_type": "hardlink", "sha256": "058794b33feeaf844f7808ca37126aa5496756a93badab301f6e0e9447e13956", "sha256_in_prefix": "058794b33feeaf844f7808ca37126aa5496756a93badab301f6e0e9447e13956", "size_in_bytes": 380482}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/nb.pak", "path_type": "hardlink", "sha256": "6f6f5f4712fae519185bd6f487e36cc61adbc53815561fefacf5301d8ad59210", "sha256_in_prefix": "6f6f5f4712fae519185bd6f487e36cc61adbc53815561fefacf5301d8ad59210", "size_in_bytes": 372143}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/nl.pak", "path_type": "hardlink", "sha256": "800d534d0412b577f642d1e4b33e13eb5569acfc3d7230046c3ec1f8388d2609", "sha256_in_prefix": "800d534d0412b577f642d1e4b33e13eb5569acfc3d7230046c3ec1f8388d2609", "size_in_bytes": 384342}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pl.pak", "path_type": "hardlink", "sha256": "3e668d95b035744d4cb5d2e7e373d89b88cf09ad35cdeb0f7b8bb9bd97e08544", "sha256_in_prefix": "3e668d95b035744d4cb5d2e7e373d89b88cf09ad35cdeb0f7b8bb9bd97e08544", "size_in_bytes": 427658}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pt-BR.pak", "path_type": "hardlink", "sha256": "dd66f7c567e5fb1d858f789c605b22a4131951b2533592835d5870c5c2635c7a", "sha256_in_prefix": "dd66f7c567e5fb1d858f789c605b22a4131951b2533592835d5870c5c2635c7a", "size_in_bytes": 404397}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/pt-PT.pak", "path_type": "hardlink", "sha256": "f6cf1416083c70ba504d07af62715104e05af856c6ac493a256c5604e68169c4", "sha256_in_prefix": "f6cf1416083c70ba504d07af62715104e05af856c6ac493a256c5604e68169c4", "size_in_bytes": 406327}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ro.pak", "path_type": "hardlink", "sha256": "c2b1aa3529e7d9f0b6414d49971371536a5507c32c7117c81bad50fbc70b0bd4", "sha256_in_prefix": "c2b1aa3529e7d9f0b6414d49971371536a5507c32c7117c81bad50fbc70b0bd4", "size_in_bytes": 418752}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ru.pak", "path_type": "hardlink", "sha256": "7d65f014244f857fb08ea37afc03307f35a9ed10e3f7848a9f0ce8443298d0f7", "sha256_in_prefix": "7d65f014244f857fb08ea37afc03307f35a9ed10e3f7848a9f0ce8443298d0f7", "size_in_bytes": 681798}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sk.pak", "path_type": "hardlink", "sha256": "d4f34d0cd0f006ad352161d532bf05289893ca6b1bacd0690c20d8db0ec80427", "sha256_in_prefix": "d4f34d0cd0f006ad352161d532bf05289893ca6b1bacd0690c20d8db0ec80427", "size_in_bytes": 432868}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sl.pak", "path_type": "hardlink", "sha256": "cc0252f1ba2d8cb61473f5ae14f92137be05c72a532442b1f47d9c09a222cb50", "sha256_in_prefix": "cc0252f1ba2d8cb61473f5ae14f92137be05c72a532442b1f47d9c09a222cb50", "size_in_bytes": 415075}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sr.pak", "path_type": "hardlink", "sha256": "6746bbada351a50c53c3b2fd2ad56123ea8ae03d8e8adca153fdb3b8019e547f", "sha256_in_prefix": "6746bbada351a50c53c3b2fd2ad56123ea8ae03d8e8adca153fdb3b8019e547f", "size_in_bytes": 642379}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sv.pak", "path_type": "hardlink", "sha256": "830a8c2ae1d6b54f040e86eb46f9de0f41a8acc1817fd5e4377696bfd9883186", "sha256_in_prefix": "830a8c2ae1d6b54f040e86eb46f9de0f41a8acc1817fd5e4377696bfd9883186", "size_in_bytes": 374553}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/sw.pak", "path_type": "hardlink", "sha256": "dc93349817d42f819af47dabb0efa13542dda9608846130493b019105f80a8e1", "sha256_in_prefix": "dc93349817d42f819af47dabb0efa13542dda9608846130493b019105f80a8e1", "size_in_bytes": 394372}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/ta.pak", "path_type": "hardlink", "sha256": "516b8b445874cd4e7e29805da40033c4be1646155385fa9cd52789e3695c4907", "sha256_in_prefix": "516b8b445874cd4e7e29805da40033c4be1646155385fa9cd52789e3695c4907", "size_in_bytes": 1012802}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/te.pak", "path_type": "hardlink", "sha256": "f1258f5e23843fdda7054de87754dded97ec1c390bcff6931a577513b033f7c2", "sha256_in_prefix": "f1258f5e23843fdda7054de87754dded97ec1c390bcff6931a577513b033f7c2", "size_in_bytes": 939905}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/th.pak", "path_type": "hardlink", "sha256": "8524a67b6721616105aea70817a7712f527f1d34e74ad011a850eea661a50482", "sha256_in_prefix": "8524a67b6721616105aea70817a7712f527f1d34e74ad011a850eea661a50482", "size_in_bytes": 790022}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/tr.pak", "path_type": "hardlink", "sha256": "4872e4a883a5dc7f5649b15083ac6d02d1a840faee3f2cfdd311d90951078da4", "sha256_in_prefix": "4872e4a883a5dc7f5649b15083ac6d02d1a840faee3f2cfdd311d90951078da4", "size_in_bytes": 401110}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/uk.pak", "path_type": "hardlink", "sha256": "9f4d753e9313199e7d785f8c5913f1325a8a86f7505e1c9fecb3e8f4cb1ddaac", "sha256_in_prefix": "9f4d753e9313199e7d785f8c5913f1325a8a86f7505e1c9fecb3e8f4cb1ddaac", "size_in_bytes": 683358}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/vi.pak", "path_type": "hardlink", "sha256": "695c71b8fa95614450aa3b4d0b12ac7300fd79e32827e9f69a13ee0fc87d1f0e", "sha256_in_prefix": "695c71b8fa95614450aa3b4d0b12ac7300fd79e32827e9f69a13ee0fc87d1f0e", "size_in_bytes": 476116}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/zh-CN.pak", "path_type": "hardlink", "sha256": "5628cbb69b27c61240ec53cd527b23f2fbc8a55dbb8f3febf2025b0b567c017c", "sha256_in_prefix": "5628cbb69b27c61240ec53cd527b23f2fbc8a55dbb8f3febf2025b0b567c017c", "size_in_bytes": 345418}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/translations/qtwebengine_locales/zh-TW.pak", "path_type": "hardlink", "sha256": "c28cc57e2c0243e722795d8480dbef528374dd14618ac10265cddd612b53a97b", "sha256_in_prefix": "c28cc57e2c0243e722795d8480dbef528374dd14618ac10265cddd612b53a97b", "size_in_bytes": 342879}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/ucrtbase.dll", "path_type": "hardlink", "sha256": "2ab2a74bcb5bfd8248d232eb3bc56698fb5173b9ff7fc0daf87d8120d0f448d7", "sha256_in_prefix": "2ab2a74bcb5bfd8248d232eb3bc56698fb5173b9ff7fc0daf87d8120d0f448d7", "size_in_bytes": 982720}, {"_path": "Library/nsight-compute/2024.1.1/host/windows-desktop-win7-x64/zlib.dll", "path_type": "hardlink", "sha256": "d0fb28bce2abe603e44472c49ecb7e29e66d1df3d42090836225213c4d6391c3", "sha256_in_prefix": "d0fb28bce2abe603e44472c49ecb7e29e66d1df3d42090836225213c4d6391c3", "size_in_bytes": 92216}, {"_path": "Library/nsight-compute/2024.1.1/ncu-ui.bat", "path_type": "hardlink", "sha256": "7bbcf82fa204c2a2613212ed12a96618aaba6403d009b3aef6b65bccbb2489e4", "sha256_in_prefix": "7bbcf82fa204c2a2613212ed12a96618aaba6403d009b3aef6b65bccbb2489e4", "size_in_bytes": 71}, {"_path": "Library/nsight-compute/2024.1.1/ncu.bat", "path_type": "hardlink", "sha256": "cbbe35bff8f28cf1dd8ea4ddc2f9097e475edea505b407f673609a3ff7627219", "sha256_in_prefix": "cbbe35bff8f28cf1dd8ea4ddc2f9097e475edea505b407f673609a3ff7627219", "size_in_bytes": 61}, {"_path": "Library/nsight-compute/2024.1.1/sections/AchievedOccupancy.py", "path_type": "hardlink", "sha256": "64144607755eb2d141c5fc59bc4d9306624747fdf209a5596ac082360662809c", "sha256_in_prefix": "64144607755eb2d141c5fc59bc4d9306624747fdf209a5596ac082360662809c", "size_in_bytes": 4978}, {"_path": "Library/nsight-compute/2024.1.1/sections/C2CLink.section", "path_type": "hardlink", "sha256": "d34d958ec216cfb3ddfec791685da7810d4b45149dbb0ff76646ae8da3779729", "sha256_in_prefix": "d34d958ec216cfb3ddfec791685da7810d4b45149dbb0ff76646ae8da3779729", "size_in_bytes": 948}, {"_path": "Library/nsight-compute/2024.1.1/sections/CPIStall.py", "path_type": "hardlink", "sha256": "1bfc6d9c87ccfe27233ba8d1b1a9ea817bd1cd94cd4acfac3aeef0d29c541d5b", "sha256_in_prefix": "1bfc6d9c87ccfe27233ba8d1b1a9ea817bd1cd94cd4acfac3aeef0d29c541d5b", "size_in_bytes": 21459}, {"_path": "Library/nsight-compute/2024.1.1/sections/Compute.py", "path_type": "hardlink", "sha256": "5eb79cd7f8d3246c573fd39218220953e271b73addf190f402272dab0f273b35", "sha256_in_prefix": "5eb79cd7f8d3246c573fd39218220953e271b73addf190f402272dab0f273b35", "size_in_bytes": 2113}, {"_path": "Library/nsight-compute/2024.1.1/sections/ComputeWorkloadAnalysis.section", "path_type": "hardlink", "sha256": "336c70d8c3a9d5aab9caadb5e9f3154cb63389ac39ab7a7a9b2b9fe382506849", "sha256_in_prefix": "336c70d8c3a9d5aab9caadb5e9f3154cb63389ac39ab7a7a9b2b9fe382506849", "size_in_bytes": 9025}, {"_path": "Library/nsight-compute/2024.1.1/sections/FPInstructions.py", "path_type": "hardlink", "sha256": "9951ff014677af944340caf97b27e03e88a4fcd91d73eeb9773469bd74be9a13", "sha256_in_prefix": "9951ff014677af944340caf97b27e03e88a4fcd91d73eeb9773469bd74be9a13", "size_in_bytes": 6466}, {"_path": "Library/nsight-compute/2024.1.1/sections/HighPipeUtilization.py", "path_type": "hardlink", "sha256": "7edf4aea2fdef5d73d3a61223b1478c0c69729e1e8b0e81b50b1e092f06d6dab", "sha256_in_prefix": "7edf4aea2fdef5d73d3a61223b1478c0c69729e1e8b0e81b50b1e092f06d6dab", "size_in_bytes": 17550}, {"_path": "Library/nsight-compute/2024.1.1/sections/InstructionStatistics.section", "path_type": "hardlink", "sha256": "2dad2e34555acc7fe11d674cb576bad757ab4bda501751912a6f7ef1aa958943", "sha256_in_prefix": "2dad2e34555acc7fe11d674cb576bad757ab4bda501751912a6f7ef1aa958943", "size_in_bytes": 2041}, {"_path": "Library/nsight-compute/2024.1.1/sections/IssueSlotUtilization.py", "path_type": "hardlink", "sha256": "feba6084d1ce78024bd508e7fbdd3ded1c1182a9bf0a79a7eb95fd53282da2d4", "sha256_in_prefix": "feba6084d1ce78024bd508e7fbdd3ded1c1182a9bf0a79a7eb95fd53282da2d4", "size_in_bytes": 5778}, {"_path": "Library/nsight-compute/2024.1.1/sections/LaunchStatistics.py", "path_type": "hardlink", "sha256": "404db3fdad8e56c7502c1e08af829fa5cc4edd1f96a58b2729e7a68e91aef2b8", "sha256_in_prefix": "404db3fdad8e56c7502c1e08af829fa5cc4edd1f96a58b2729e7a68e91aef2b8", "size_in_bytes": 8275}, {"_path": "Library/nsight-compute/2024.1.1/sections/LaunchStatistics.section", "path_type": "hardlink", "sha256": "4c26de70db11ff43330e8e30a1b68d53dedcb88f2a3a0e376b51624b8a36fd08", "sha256_in_prefix": "4c26de70db11ff43330e8e30a1b68d53dedcb88f2a3a0e376b51624b8a36fd08", "size_in_bytes": 2194}, {"_path": "Library/nsight-compute/2024.1.1/sections/Memory.py", "path_type": "hardlink", "sha256": "38515fee31b3b935402f64cb656353264a09c3d72223c2e3a35844df5b943203", "sha256_in_prefix": "38515fee31b3b935402f64cb656353264a09c3d72223c2e3a35844df5b943203", "size_in_bytes": 2583}, {"_path": "Library/nsight-compute/2024.1.1/sections/MemoryApertureUsage.py", "path_type": "hardlink", "sha256": "fc9a9ae758bfeaf4a0e80f384d4fcd6f54cde170d23b5d25e9c12d1037740f72", "sha256_in_prefix": "fc9a9ae758bfeaf4a0e80f384d4fcd6f54cde170d23b5d25e9c12d1037740f72", "size_in_bytes": 7542}, {"_path": "Library/nsight-compute/2024.1.1/sections/MemoryCacheAccessPattern.py", "path_type": "hardlink", "sha256": "36dfcd02c21157b4e3c1d079a81b88fc42345f41a54830538f8e46289dacabfb", "sha256_in_prefix": "36dfcd02c21157b4e3c1d079a81b88fc42345f41a54830538f8e46289dacabfb", "size_in_bytes": 16475}, {"_path": "Library/nsight-compute/2024.1.1/sections/MemoryL2Compression.py", "path_type": "hardlink", "sha256": "464c01886cc6114f6e3266fc1e4f1e3d417d5c18e0c179e8e4f2f684356256c4", "sha256_in_prefix": "464c01886cc6114f6e3266fc1e4f1e3d417d5c18e0c179e8e4f2f684356256c4", "size_in_bytes": 7581}, {"_path": "Library/nsight-compute/2024.1.1/sections/MemoryWorkloadAnalysis.section", "path_type": "hardlink", "sha256": "6364f19ab38f0eda544133a283ca585517cf6af6f2552b02f253e8e542306a49", "sha256_in_prefix": "6364f19ab38f0eda544133a283ca585517cf6af6f2552b02f253e8e542306a49", "size_in_bytes": 1818}, {"_path": "Library/nsight-compute/2024.1.1/sections/MemoryWorkloadAnalysis_Chart.section", "path_type": "hardlink", "sha256": "e4c6cdad7b1173344b67cdd6ffa33bdc4755c58efa33ddb7ef9d61695dd9e12d", "sha256_in_prefix": "e4c6cdad7b1173344b67cdd6ffa33bdc4755c58efa33ddb7ef9d61695dd9e12d", "size_in_bytes": 1883}, {"_path": "Library/nsight-compute/2024.1.1/sections/MemoryWorkloadAnalysis_Tables.section", "path_type": "hardlink", "sha256": "abe53ac6c15184d39cdb907c150d828ad676918b9658c2d3ba32b75783c4b2ff", "sha256_in_prefix": "abe53ac6c15184d39cdb907c150d828ad676918b9658c2d3ba32b75783c4b2ff", "size_in_bytes": 3978}, {"_path": "Library/nsight-compute/2024.1.1/sections/NumaAffinity.section", "path_type": "hardlink", "sha256": "a3080bb34cce2585fde1047498a924fab3708af01c10f6a7e2df4eb21fb6b4c0", "sha256_in_prefix": "a3080bb34cce2585fde1047498a924fab3708af01c10f6a7e2df4eb21fb6b4c0", "size_in_bytes": 975}, {"_path": "Library/nsight-compute/2024.1.1/sections/NvRules.py", "path_type": "hardlink", "sha256": "e9d95de9d2d749c6dfaec5b0471c8e6a0a7a62dfbe81b82074dd2496498d19ae", "sha256_in_prefix": "e9d95de9d2d749c6dfaec5b0471c8e6a0a7a62dfbe81b82074dd2496498d19ae", "size_in_bytes": 113975}, {"_path": "Library/nsight-compute/2024.1.1/sections/Nvlink.section", "path_type": "hardlink", "sha256": "fc9d7ba9b23d678425224ace212a99fa46f3ed73ceaee4c5ccff0f7a1bfeb414", "sha256_in_prefix": "fc9d7ba9b23d678425224ace212a99fa46f3ed73ceaee4c5ccff0f7a1bfeb414", "size_in_bytes": 4587}, {"_path": "Library/nsight-compute/2024.1.1/sections/Nvlink_Tables.section", "path_type": "hardlink", "sha256": "36b3ffbc338078513657152cf7358a3ed7a215f2a33b49881940a97902217c12", "sha256_in_prefix": "36b3ffbc338078513657152cf7358a3ed7a215f2a33b49881940a97902217c12", "size_in_bytes": 451}, {"_path": "Library/nsight-compute/2024.1.1/sections/Nvlink_Topology.section", "path_type": "hardlink", "sha256": "54195e7412f8c649f3c53d2d65f7c557a2cd13870fcc52be8adf27c71df68c66", "sha256_in_prefix": "54195e7412f8c649f3c53d2d65f7c557a2cd13870fcc52be8adf27c71df68c66", "size_in_bytes": 410}, {"_path": "Library/nsight-compute/2024.1.1/sections/Occupancy.section", "path_type": "hardlink", "sha256": "60923f104984b71d7b1b94344ebe78e00951c7fcb9096106f53f3b7f44fe46e9", "sha256_in_prefix": "60923f104984b71d7b1b94344ebe78e00951c7fcb9096106f53f3b7f44fe46e9", "size_in_bytes": 5512}, {"_path": "Library/nsight-compute/2024.1.1/sections/PCSamplingData.py", "path_type": "hardlink", "sha256": "594a88d6e1d18756cb99436db157dc4c8093aa56069f653426e8af906b4ae5d8", "sha256_in_prefix": "594a88d6e1d18756cb99436db157dc4c8093aa56069f653426e8af906b4ae5d8", "size_in_bytes": 2825}, {"_path": "Library/nsight-compute/2024.1.1/sections/PmSampling.section", "path_type": "hardlink", "sha256": "1f01a7e1e2690dab72be99f556022d5e6bc5d2497e7ea1349c421beef53a05b6", "sha256_in_prefix": "1f01a7e1e2690dab72be99f556022d5e6bc5d2497e7ea1349c421beef53a05b6", "size_in_bytes": 14889}, {"_path": "Library/nsight-compute/2024.1.1/sections/PmSampling_WarpStates.section", "path_type": "hardlink", "sha256": "fc897d7fab9ae24c87e6a00fd5d55c15975146f392bd970700c2c8c740f46e73", "sha256_in_prefix": "fc897d7fab9ae24c87e6a00fd5d55c15975146f392bd970700c2c8c740f46e73", "size_in_bytes": 8116}, {"_path": "Library/nsight-compute/2024.1.1/sections/RequestedMetrics.py", "path_type": "hardlink", "sha256": "7fc365f08512410960751d89692baa5aa5e55018b5906076a565b7956e669dbf", "sha256_in_prefix": "7fc365f08512410960751d89692baa5aa5e55018b5906076a565b7956e669dbf", "size_in_bytes": 9581}, {"_path": "Library/nsight-compute/2024.1.1/sections/SchedulerStatistics.section", "path_type": "hardlink", "sha256": "3c78d2bc0f22b0780f15402b8241f275c5d106dc4a38307368537acd7cf81e89", "sha256_in_prefix": "3c78d2bc0f22b0780f15402b8241f275c5d106dc4a38307368537acd7cf81e89", "size_in_bytes": 2396}, {"_path": "Library/nsight-compute/2024.1.1/sections/SharedMemoryConflicts.py", "path_type": "hardlink", "sha256": "a8e6fe8c1753bc11a25bf50abc52c7ffd95f2178f31ec52708fd808d16905f45", "sha256_in_prefix": "a8e6fe8c1753bc11a25bf50abc52c7ffd95f2178f31ec52708fd808d16905f45", "size_in_bytes": 6362}, {"_path": "Library/nsight-compute/2024.1.1/sections/SlowPipeLimiter.py", "path_type": "hardlink", "sha256": "ffc0a5c688c304ef75c9cc521a96ac64465e0bcd2704c0da479001522f047059", "sha256_in_prefix": "ffc0a5c688c304ef75c9cc521a96ac64465e0bcd2704c0da479001522f047059", "size_in_bytes": 5287}, {"_path": "Library/nsight-compute/2024.1.1/sections/SourceCounters.section", "path_type": "hardlink", "sha256": "9d6e90a8a2bd95a1086f4c024132650195bbb091d13bd2fc774aac05b693df82", "sha256_in_prefix": "9d6e90a8a2bd95a1086f4c024132650195bbb091d13bd2fc774aac05b693df82", "size_in_bytes": 17136}, {"_path": "Library/nsight-compute/2024.1.1/sections/SpeedOfLight.py", "path_type": "hardlink", "sha256": "935270d3174cbc454cdc77c25fef946230313edf0a53e67f7f7382a1e7a7fe87", "sha256_in_prefix": "935270d3174cbc454cdc77c25fef946230313edf0a53e67f7f7382a1e7a7fe87", "size_in_bytes": 8788}, {"_path": "Library/nsight-compute/2024.1.1/sections/SpeedOfLight.section", "path_type": "hardlink", "sha256": "fd72e6755dbe9a0ad2a3064bcef84dfd90713a05e84fba5c96cb935f79c84b68", "sha256_in_prefix": "fd72e6755dbe9a0ad2a3064bcef84dfd90713a05e84fba5c96cb935f79c84b68", "size_in_bytes": 3865}, {"_path": "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_HierarchicalDoubleRooflineChart.section", "path_type": "hardlink", "sha256": "0f74c8222c6caace14e52cdcf60673c2d68c5b77709c72c01b6de2af055bc020", "sha256_in_prefix": "0f74c8222c6caace14e52cdcf60673c2d68c5b77709c72c01b6de2af055bc020", "size_in_bytes": 13141}, {"_path": "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_HierarchicalHalfRooflineChart.section", "path_type": "hardlink", "sha256": "3fd5841987bf5bbef853d67e6510f087aabdf6fb3c21e06ae6211dff0d9a4db6", "sha256_in_prefix": "3fd5841987bf5bbef853d67e6510f087aabdf6fb3c21e06ae6211dff0d9a4db6", "size_in_bytes": 13133}, {"_path": "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_HierarchicalSingleRooflineChart.section", "path_type": "hardlink", "sha256": "bee6894a77ce596744b1a436563e89d1a537c7902c830247a30f29e54b835767", "sha256_in_prefix": "bee6894a77ce596744b1a436563e89d1a537c7902c830247a30f29e54b835767", "size_in_bytes": 13141}, {"_path": "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_HierarchicalTensorRooflineChart.section", "path_type": "hardlink", "sha256": "aa625ad01e9d20d79633c43b55c445455eab9a15da8e97f02a3579381eaa8c45", "sha256_in_prefix": "aa625ad01e9d20d79633c43b55c445455eab9a15da8e97f02a3579381eaa8c45", "size_in_bytes": 11400}, {"_path": "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_Roofline.py", "path_type": "hardlink", "sha256": "8479e303cdd501844c9f2f26963fa1d5c0822d62a1b3d401b6ae0532c21761ba", "sha256_in_prefix": "8479e303cdd501844c9f2f26963fa1d5c0822d62a1b3d401b6ae0532c21761ba", "size_in_bytes": 7571}, {"_path": "Library/nsight-compute/2024.1.1/sections/SpeedOfLight_RooflineChart.section", "path_type": "hardlink", "sha256": "aa2e015a035fcaf400b71fc7a1a95be7c7ec471e278aeef1dc056ea4016c41bf", "sha256_in_prefix": "aa2e015a035fcaf400b71fc7a1a95be7c7ec471e278aeef1dc056ea4016c41bf", "size_in_bytes": 9129}, {"_path": "Library/nsight-compute/2024.1.1/sections/TheoreticalOccupancy.py", "path_type": "hardlink", "sha256": "e5c125306c06a2e8bac358af1452a8598493f791b40ec69bf5349691691ff00c", "sha256_in_prefix": "e5c125306c06a2e8bac358af1452a8598493f791b40ec69bf5349691691ff00c", "size_in_bytes": 5442}, {"_path": "Library/nsight-compute/2024.1.1/sections/ThreadDivergence.py", "path_type": "hardlink", "sha256": "82545cd163b05f909dace3812a3458114030980027f997fdb387c4a0b9c453ad", "sha256_in_prefix": "82545cd163b05f909dace3812a3458114030980027f997fdb387c4a0b9c453ad", "size_in_bytes": 5074}, {"_path": "Library/nsight-compute/2024.1.1/sections/UncoalescedAccess.chart", "path_type": "hardlink", "sha256": "93beab947b9f54ff613a9f739c6862e6c15d01f5acf1fe974af19f321aa1aa40", "sha256_in_prefix": "93beab947b9f54ff613a9f739c6862e6c15d01f5acf1fe974af19f321aa1aa40", "size_in_bytes": 161}, {"_path": "Library/nsight-compute/2024.1.1/sections/UncoalescedAccess.py", "path_type": "hardlink", "sha256": "5997292eeea2392fe5b6f49b6fa1be104ddc091dfd9a6268e7539f6cd3e403f7", "sha256_in_prefix": "5997292eeea2392fe5b6f49b6fa1be104ddc091dfd9a6268e7539f6cd3e403f7", "size_in_bytes": 7096}, {"_path": "Library/nsight-compute/2024.1.1/sections/UncoalescedSharedAccess.chart", "path_type": "hardlink", "sha256": "05cdaf18c975f32a5dbc482393970eb499c6968909da14f8a5ae851073faf99c", "sha256_in_prefix": "05cdaf18c975f32a5dbc482393970eb499c6968909da14f8a5ae851073faf99c", "size_in_bytes": 143}, {"_path": "Library/nsight-compute/2024.1.1/sections/UncoalescedSharedAccess.py", "path_type": "hardlink", "sha256": "60ba8f9703333aeff7fd8c0338d3ca058de2bdf447033c74f8946899d20dbedc", "sha256_in_prefix": "60ba8f9703333aeff7fd8c0338d3ca058de2bdf447033c74f8946899d20dbedc", "size_in_bytes": 7432}, {"_path": "Library/nsight-compute/2024.1.1/sections/WarpStateStatistics.section", "path_type": "hardlink", "sha256": "a654480f0ac77667c1aceae95a07d12e1e2d4e3fc0cd3a4fc86085947c4f20e2", "sha256_in_prefix": "a654480f0ac77667c1aceae95a07d12e1e2d4e3fc0cd3a4fc86085947c4f20e2", "size_in_bytes": 4671}, {"_path": "Library/nsight-compute/2024.1.1/sections/WorkloadDistribution.section", "path_type": "hardlink", "sha256": "f2954c8b47113d8f75146cf4d33fe8468ad4fdd60b7810581f5e36264d53be65", "sha256_in_prefix": "f2954c8b47113d8f75146cf4d33fe8468ad4fdd60b7810581f5e36264d53be65", "size_in_bytes": 3661}, {"_path": "Library/nsight-compute/2024.1.1/sections/WorkloadImbalance.py", "path_type": "hardlink", "sha256": "00b85051c21dbc77144c9d57203f58de83efa4052f16c45eda04cbce13a1228b", "sha256_in_prefix": "00b85051c21dbc77144c9d57203f58de83efa4052f16c45eda04cbce13a1228b", "size_in_bytes": 8905}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/TreeLauncherSubreaper", "path_type": "hardlink", "sha256": "fb961fcb1ccd32464d8cbae9962f27c498457218a177ca0837230a8f46ff20c8", "sha256_in_prefix": "fb961fcb1ccd32464d8cbae9962f27c498457218a177ca0837230a8f46ff20c8", "size_in_bytes": 166776}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/TreeLauncherTargetLdPreloadHelper", "path_type": "hardlink", "sha256": "49b3cdad1e31bd664a3aa8c985b5b9bdefaace38068b507ef735221ecf284427", "sha256_in_prefix": "49b3cdad1e31bd664a3aa8c985b5b9bdefaace38068b507ef735221ecf284427", "size_in_bytes": 14336}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libInterceptorInjectionTarget.so", "path_type": "hardlink", "sha256": "f90a24bda7c0b0478443a520f41fcaed151fbe7b4140f14a5e90ebf61ee6ddd6", "sha256_in_prefix": "f90a24bda7c0b0478443a520f41fcaed151fbe7b4140f14a5e90ebf61ee6ddd6", "size_in_bytes": 244944}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherPlaceholder.so", "path_type": "hardlink", "sha256": "e86d0db97534ddc17115e52fed2b88fa0d5bc66072568a1187d1c19e5dda2c9a", "sha256_in_prefix": "e86d0db97534ddc17115e52fed2b88fa0d5bc66072568a1187d1c19e5dda2c9a", "size_in_bytes": 14000}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherTargetInjection.so", "path_type": "hardlink", "sha256": "b379562186223e4413ef515184708535a3cd71e6ade236530f16c19ef9d3e668", "sha256_in_prefix": "b379562186223e4413ef515184708535a3cd71e6ade236530f16c19ef9d3e668", "size_in_bytes": 2980648}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libTreeLauncherTargetUpdatePreloadInjection.so", "path_type": "hardlink", "sha256": "f57dfaa888e33cf086ba14bcd33d72534f7878903da158da96432baa50c99be7", "sha256_in_prefix": "f57dfaa888e33cf086ba14bcd33d72534f7878903da158da96432baa50c99be7", "size_in_bytes": 1392272}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libcuda-injection.so", "path_type": "hardlink", "sha256": "e273f70f9aac6456a6f1399b4d8a86a9063a7fa3b2a294706b0e15cf17fe1407", "sha256_in_prefix": "e273f70f9aac6456a6f1399b4d8a86a9063a7fa3b2a294706b0e15cf17fe1407", "size_in_bytes": 54160208}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libnvperf_host.so", "path_type": "hardlink", "sha256": "8323164a42d872ff4239de0dd786be115a482d1a226d73196593577434513f64", "sha256_in_prefix": "8323164a42d872ff4239de0dd786be115a482d1a226d73196593577434513f64", "size_in_bytes": 28125280}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/libnvperf_target.so", "path_type": "hardlink", "sha256": "a8f78c8dff043d84c5418742b146512bd98b305f085d08de9d71b733136d7e88", "sha256_in_prefix": "a8f78c8dff043d84c5418742b146512bd98b305f085d08de9d71b733136d7e88", "size_in_bytes": 5555536}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x64/ncu", "path_type": "hardlink", "sha256": "e2c04f5b1ebe7cec85e5f4b48c73cacc4e01dcb58a765fd54d9c362498f609d0", "sha256_in_prefix": "e2c04f5b1ebe7cec85e5f4b48c73cacc4e01dcb58a765fd54d9c362498f609d0", "size_in_bytes": 88968432}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/TreeLauncherTargetLdPreloadHelper", "path_type": "hardlink", "sha256": "35c6740198024910230154b96ba4d28afc6397b789dba4ec89631189f5eb92b2", "sha256_in_prefix": "35c6740198024910230154b96ba4d28afc6397b789dba4ec89631189f5eb92b2", "size_in_bytes": 13696}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/libInterceptorInjectionTarget.so", "path_type": "hardlink", "sha256": "42829244909d3b6c3ad0eb0cc9b70d8edf4d89c8c14e0d8709c60a2b3ad35859", "sha256_in_prefix": "42829244909d3b6c3ad0eb0cc9b70d8edf4d89c8c14e0d8709c60a2b3ad35859", "size_in_bytes": 264156}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherPlaceholder.so", "path_type": "hardlink", "sha256": "39618c7ff5c62f5bc7ee88b384ae92545ba70760f6fbaebf0432abb2cfeca198", "sha256_in_prefix": "39618c7ff5c62f5bc7ee88b384ae92545ba70760f6fbaebf0432abb2cfeca198", "size_in_bytes": 13432}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherTargetInjection.so", "path_type": "hardlink", "sha256": "a14343f55b5f0ef894a80de8411a029371d8615963b25212c214e3c107bcebb0", "sha256_in_prefix": "a14343f55b5f0ef894a80de8411a029371d8615963b25212c214e3c107bcebb0", "size_in_bytes": 3186656}, {"_path": "Library/nsight-compute/2024.1.1/target/linux-desktop-glibc_2_11_3-x86/libTreeLauncherTargetUpdatePreloadInjection.so", "path_type": "hardlink", "sha256": "70a9ba62baddf27b1803d4dea22eed69ddd8f24a5a2dd5ba7e2c1b8782be9748", "sha256_in_prefix": "70a9ba62baddf27b1803d4dea22eed69ddd8f24a5a2dd5ba7e2c1b8782be9748", "size_in_bytes": 1480952}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/InterceptorInjectionTarget.dll", "path_type": "hardlink", "sha256": "6fc27b2cabe28d5c15ae6d57fd7aef207b4b910b8d6fe184170827a07a52901c", "sha256_in_prefix": "6fc27b2cabe28d5c15ae6d57fd7aef207b4b910b8d6fe184170827a07a52901c", "size_in_bytes": 1128048}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/TreeLauncherTargetInjection.dll", "path_type": "hardlink", "sha256": "5de56b1e2a61ae83a6bc8be4fedae69f48c5fff9fdac53b79b834a5346b24ccc", "sha256_in_prefix": "5de56b1e2a61ae83a6bc8be4fedae69f48c5fff9fdac53b79b834a5346b24ccc", "size_in_bytes": 1605232}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/cuda-injection.dll", "path_type": "hardlink", "sha256": "9244a1a92fd1ab63ddd759ae354838508cc0c16c0b229f85c6a6c04c1a2166df", "sha256_in_prefix": "9244a1a92fd1ab63ddd759ae354838508cc0c16c0b229f85c6a6c04c1a2166df", "size_in_bytes": 49761368}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/icudt71.dll", "path_type": "hardlink", "sha256": "6bac5253bf113a1d83408c009da5c2263d36f0009f8baa3e8aa4c157a0e4fa13", "sha256_in_prefix": "6bac5253bf113a1d83408c009da5c2263d36f0009f8baa3e8aa4c157a0e4fa13", "size_in_bytes": 30432368}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/icuuc71.dll", "path_type": "hardlink", "sha256": "f363f02722f4f0fe69b40bd650c5bd0c0faf8beaf79e6ed17f6f78d6df8e7cb6", "sha256_in_prefix": "f363f02722f4f0fe69b40bd650c5bd0c0faf8beaf79e6ed17f6f78d6df8e7cb6", "size_in_bytes": 2263152}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/ncu.exe", "path_type": "hardlink", "sha256": "be2ccb4c034969e3e6f568c49bc335c5c3ae0606e9c6ca7561106843cbe6e995", "sha256_in_prefix": "be2ccb4c034969e3e6f568c49bc335c5c3ae0606e9c6ca7561106843cbe6e995", "size_in_bytes": 52900432}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/nvperf_host.dll", "path_type": "hardlink", "sha256": "3cbf8fa2d819b2d4342ec7d7e5a2b82087450d46fab11442ccf72687c9562570", "sha256_in_prefix": "3cbf8fa2d819b2d4342ec7d7e5a2b82087450d46fab11442ccf72687c9562570", "size_in_bytes": 22554224}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x64/nvperf_target.dll", "path_type": "hardlink", "sha256": "4bb9a293454a61302f6eec32dda10902262835bd3d065204dde66b2bf0aaff3e", "sha256_in_prefix": "4bb9a293454a61302f6eec32dda10902262835bd3d065204dde66b2bf0aaff3e", "size_in_bytes": 2000496}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x86/InterceptorInjectionTarget.dll", "path_type": "hardlink", "sha256": "a81e3e2ec7b6a3a3fbc217e04d681484b3f7a8edea1b4451fe7411ce122d6dfe", "sha256_in_prefix": "a81e3e2ec7b6a3a3fbc217e04d681484b3f7a8edea1b4451fe7411ce122d6dfe", "size_in_bytes": 939120}, {"_path": "Library/nsight-compute/2024.1.1/target/windows-desktop-win7-x86/TreeLauncherTargetInjection.dll", "path_type": "hardlink", "sha256": "c94fae755dcb5fb57d8e07f981e981ae5e737fd83ebd9fe54d6646625bcc337e", "sha256_in_prefix": "c94fae755dcb5fb57d8e07f981e981ae5e737fd83ebd9fe54d6646625bcc337e", "size_in_bytes": 1404016}, {"_path": "Scripts/CrashReporter.bat", "path_type": "hardlink", "sha256": "1a03c787425d3a95540adfbb5bd9006bfdc6692ccb30102f669873d9b604c1a5", "sha256_in_prefix": "1a03c787425d3a95540adfbb5bd9006bfdc6692ccb30102f669873d9b604c1a5", "size_in_bytes": 75}, {"_path": "Scripts/CudaGpuInfoDumper.bat", "path_type": "hardlink", "sha256": "e8e882e49e1ec38122a7f35dafc482a28a06775875e652b4b104097cedf1454d", "sha256_in_prefix": "e8e882e49e1ec38122a7f35dafc482a28a06775875e652b4b104097cedf1454d", "size_in_bytes": 79}, {"_path": "Scripts/QdstrmImporter.bat", "path_type": "hardlink", "sha256": "ab269a7dbeba8d6baf973a9721a5a62ffd39a4318bd3f8eada9aa5f4e7528b8a", "sha256_in_prefix": "ab269a7dbeba8d6baf973a9721a5a62ffd39a4318bd3f8eada9aa5f4e7528b8a", "size_in_bytes": 76}, {"_path": "Scripts/QtWebEngineProcess.bat", "path_type": "hardlink", "sha256": "c1fb4549656214c46138911e54093b7d42225fced5469a8405db015c4c3a9a66", "sha256_in_prefix": "c1fb4549656214c46138911e54093b7d42225fced5469a8405db015c4c3a9a66", "size_in_bytes": 80}, {"_path": "Scripts/ncu-ui.bat", "path_type": "hardlink", "sha256": "4e2f5c87b715c760826e699f429a681b4ebc802a0fb6ae9ac65d93feb0b7aba2", "sha256_in_prefix": "4e2f5c87b715c760826e699f429a681b4ebc802a0fb6ae9ac65d93feb0b7aba2", "size_in_bytes": 68}, {"_path": "Scripts/ncu.bat", "path_type": "hardlink", "sha256": "775add56294fa7ea550c131b0e83ac603153bc13b02f1780b6a2ad6696b47e14", "sha256_in_prefix": "775add56294fa7ea550c131b0e83ac603153bc13b02f1780b6a2ad6696b47e14", "size_in_bytes": 65}, {"_path": "Scripts/nsight-sys-service.bat", "path_type": "hardlink", "sha256": "31dedb957fed646dfc530d4f6a00cf45b7e77db0887401cfd934f60817e5b03b", "sha256_in_prefix": "31dedb957fed646dfc530d4f6a00cf45b7e77db0887401cfd934f60817e5b03b", "size_in_bytes": 80}, {"_path": "Scripts/nsys.bat", "path_type": "hardlink", "sha256": "255e48861316a7838745bb6366e84212d5bc7af8ed620c180bba88678e6e81e2", "sha256_in_prefix": "255e48861316a7838745bb6366e84212d5bc7af8ed620c180bba88678e6e81e2", "size_in_bytes": 66}, {"_path": "Scripts/python.bat", "path_type": "hardlink", "sha256": "95c17df772eff9e66c9109f99d0e56454ea9160c3f8bedcf7093cb0bd4e102ea", "sha256_in_prefix": "95c17df772eff9e66c9109f99d0e56454ea9160c3f8bedcf7093cb0bd4e102ea", "size_in_bytes": 68}, {"_path": "Scripts/sqlite3.bat", "path_type": "hardlink", "sha256": "524f03db708191915be6afe61907788405633033b3f4252612c5fc03bdd7fc56", "sha256_in_prefix": "524f03db708191915be6afe61907788405633033b3f4252612c5fc03bdd7fc56", "size_in_bytes": 69}], "paths_version": 1}, "requested_spec": "None", "sha256": "626f7bb18e45f0dfdf6320bb3b5f4c4b2a099efe89de6291bf17d11b80d1b0a7", "size": 503429300, "subdir": "win-64", "timestamp": 1715726316000, "url": "https://repo.anaconda.com/pkgs/main/win-64/nsight-compute-2024.1.1.4-hb5e1e24_2.conda", "version": "2024.1.1.4"}