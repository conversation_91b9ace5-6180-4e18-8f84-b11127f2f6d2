{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-cupti", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvprof-12.4.127-hd77b12b_1", "files": ["Library/bin/cuinj64_124.dll", "Library/bin/nvprof.exe"], "fn": "cuda-nvprof-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvprof-12.4.127-hd77b12b_1", "type": 1}, "md5": "bb78d9c64f1b29faa8f9d843d0664594", "name": "cuda-nvprof", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvprof-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/cuinj64_124.dll", "path_type": "hardlink", "sha256": "5dc11bd8c0a2c8e18b454b8e2fcee19cf1f515349d4f8fd149da25e7577a94a8", "sha256_in_prefix": "5dc11bd8c0a2c8e18b454b8e2fcee19cf1f515349d4f8fd149da25e7577a94a8", "size_in_bytes": 1513472}, {"_path": "Library/bin/nvprof.exe", "path_type": "hardlink", "sha256": "31d80b58b35650e1f5e9be29125740f5fa4e9000b58dd761d8f0edc69fe44561", "sha256_in_prefix": "31d80b58b35650e1f5e9be29125740f5fa4e9000b58dd761d8f0edc69fe44561", "size_in_bytes": 2207744}], "paths_version": 1}, "requested_spec": "None", "sha256": "93e2d9e5dfa89df305f981dedd171efdd531c3ccb070f581b49287109f5e3f05", "size": 1491244, "subdir": "win-64", "timestamp": 1715372384000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvprof-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}