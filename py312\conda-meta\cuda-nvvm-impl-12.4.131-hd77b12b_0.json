{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvvm-impl-12.4.131-hd77b12b_0", "files": ["Library/nvvm/include/nvvm.h", "Library/nvvm/lib/x64/nvvm.lib"], "fn": "cuda-nvvm-impl-12.4.131-hd77b12b_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvvm-impl-12.4.131-hd77b12b_0", "type": 1}, "md5": "1f394d82ba72d321cea0f262017b9da3", "name": "cuda-nvvm-impl", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvvm-impl-12.4.131-hd77b12b_0.conda", "paths_data": {"paths": [{"_path": "Library/nvvm/include/nvvm.h", "path_type": "hardlink", "sha256": "9c00dbcb0ce0bdecc92599fe8431ad9a2774b418a6273ad33253a82fed9a1a6e", "sha256_in_prefix": "9c00dbcb0ce0bdecc92599fe8431ad9a2774b418a6273ad33253a82fed9a1a6e", "size_in_bytes": 11963}, {"_path": "Library/nvvm/lib/x64/nvvm.lib", "path_type": "hardlink", "sha256": "5c2ab43bc74c0033374eef917394e7d9df0709b4abfa532be53d4db7141a4c25", "sha256_in_prefix": "5c2ab43bc74c0033374eef917394e7d9df0709b4abfa532be53d4db7141a4c25", "size_in_bytes": 4922}], "paths_version": 1}, "requested_spec": "None", "sha256": "07badb595ea78dbea0494dbe61487b94ee17c53db4f74e1cda2480f668d05bfe", "size": 27531, "subdir": "win-64", "timestamp": 1714770035000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvvm-impl-12.4.131-hd77b12b_0.conda", "version": "12.4.131"}