{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-nvrtc", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcublas-12.4.5.8-hd77b12b_1", "files": ["Library/bin/cublas64_12.dll", "Library/bin/cublasLt64_12.dll", "Library/bin/nvblas64_12.dll"], "fn": "libcublas-12.4.5.8-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcublas-12.4.5.8-hd77b12b_1", "type": 1}, "md5": "1e9e6d4597d5bbc3d89865176944be0a", "name": "libcublas", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcublas-12.4.5.8-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/cublas64_12.dll", "path_type": "hardlink", "sha256": "e40202fe4223c1cd2d2dce7beec59e1ed61c7801bd827309183be9b50e358f4c", "sha256_in_prefix": "e40202fe4223c1cd2d2dce7beec59e1ed61c7801bd827309183be9b50e358f4c", "size_in_bytes": 100033536}, {"_path": "Library/bin/cublasLt64_12.dll", "path_type": "hardlink", "sha256": "2a896460bef60ed57ef32b0875812f355a6984e671d638bb632f5e8c1d7a831f", "sha256_in_prefix": "2a896460bef60ed57ef32b0875812f355a6984e671d638bb632f5e8c1d7a831f", "size_in_bytes": 473551360}, {"_path": "Library/bin/nvblas64_12.dll", "path_type": "hardlink", "sha256": "e42a77405e6e4b1cc661dcfcddead35ec62dcf59c6f9be1a3b5fab73d1f4c616", "sha256_in_prefix": "e42a77405e6e4b1cc661dcfcddead35ec62dcf59c6f9be1a3b5fab73d1f4c616", "size_in_bytes": 331776}], "paths_version": 1}, "requested_spec": "None", "sha256": "4951c852af7a2307b64d523a310b37779ab054c879ef0ade89010df160d048fb", "size": 289009900, "subdir": "win-64", "timestamp": 1714775668000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcublas-12.4.5.8-hd77b12b_1.conda", "version": "12.4.5.8"}