{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libnpp 12.2.5.30 hd77b12b_1"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnpp-dev-12.2.5.30-hd77b12b_1", "files": ["Library/include/npp.h", "Library/include/nppcore.h", "Library/include/nppdefs.h", "Library/include/nppi.h", "Library/include/nppi_arithmetic_and_logical_operations.h", "Library/include/nppi_color_conversion.h", "Library/include/nppi_data_exchange_and_initialization.h", "Library/include/nppi_filtering_functions.h", "Library/include/nppi_geometry_transforms.h", "Library/include/nppi_linear_transforms.h", "Library/include/nppi_morphological_operations.h", "Library/include/nppi_statistics_functions.h", "Library/include/nppi_support_functions.h", "Library/include/nppi_threshold_and_compare_operations.h", "Library/include/npps.h", "Library/include/npps_arithmetic_and_logical_operations.h", "Library/include/npps_conversion_functions.h", "Library/include/npps_filtering_functions.h", "Library/include/npps_initialization.h", "Library/include/npps_statistics_functions.h", "Library/include/npps_support_functions.h", "Library/lib/nppc.lib", "Library/lib/nppial.lib", "Library/lib/nppicc.lib", "Library/lib/nppidei.lib", "Library/lib/nppif.lib", "Library/lib/nppig.lib", "Library/lib/nppim.lib", "Library/lib/nppist.lib", "Library/lib/nppisu.lib", "Library/lib/nppitc.lib", "Library/lib/npps.lib"], "fn": "libnpp-dev-12.2.5.30-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnpp-dev-12.2.5.30-hd77b12b_1", "type": 1}, "md5": "a4025e6fc0b00a1fafd1654f88113104", "name": "libnpp-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnpp-dev-12.2.5.30-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/include/npp.h", "path_type": "hardlink", "sha256": "2ad4744ad8106afea2077848155af596beab5100c493e37147a96ea6ad9d57cc", "sha256_in_prefix": "2ad4744ad8106afea2077848155af596beab5100c493e37147a96ea6ad9d57cc", "size_in_bytes": 3531}, {"_path": "Library/include/nppcore.h", "path_type": "hardlink", "sha256": "006993ca1ee1a8a435e6ca83f57787ba6f6d72236da587f60d253b2959648bce", "sha256_in_prefix": "006993ca1ee1a8a435e6ca83f57787ba6f6d72236da587f60d253b2959648bce", "size_in_bytes": 7618}, {"_path": "Library/include/nppdefs.h", "path_type": "hardlink", "sha256": "286fcda0cde9f90ab94673bb4c260f41ed419127a94da628d7ef49e9a1514499", "sha256_in_prefix": "286fcda0cde9f90ab94673bb4c260f41ed419127a94da628d7ef49e9a1514499", "size_in_bytes": 42514}, {"_path": "Library/include/nppi.h", "path_type": "hardlink", "sha256": "c2ee3d052f2ceca1396426c39b8f66ff2f0a92c7f6364feada9878c148b7a8ac", "sha256_in_prefix": "c2ee3d052f2ceca1396426c39b8f66ff2f0a92c7f6364feada9878c148b7a8ac", "size_in_bytes": 4181}, {"_path": "Library/include/nppi_arithmetic_and_logical_operations.h", "path_type": "hardlink", "sha256": "d640873fac8a805b666050d24f8e7ca0da173b0bd2580cea14ce8e165e2b6a1e", "sha256_in_prefix": "d640873fac8a805b666050d24f8e7ca0da173b0bd2580cea14ce8e165e2b6a1e", "size_in_bytes": 1493821}, {"_path": "Library/include/nppi_color_conversion.h", "path_type": "hardlink", "sha256": "84ddea5ecf450a615241615e74b450205b13601871040e072566ca7da9976828", "sha256_in_prefix": "84ddea5ecf450a615241615e74b450205b13601871040e072566ca7da9976828", "size_in_bytes": 985993}, {"_path": "Library/include/nppi_data_exchange_and_initialization.h", "path_type": "hardlink", "sha256": "f728330f1fa94cd915434d295fa0afc5e1031b6e82558ea0e8eb007300f20829", "sha256_in_prefix": "f728330f1fa94cd915434d295fa0afc5e1031b6e82558ea0e8eb007300f20829", "size_in_bytes": 416065}, {"_path": "Library/include/nppi_filtering_functions.h", "path_type": "hardlink", "sha256": "12256942c3d76f2473779fdc45be6464b040e8a50a7046707d4ff805f5485d9e", "sha256_in_prefix": "12256942c3d76f2473779fdc45be6464b040e8a50a7046707d4ff805f5485d9e", "size_in_bytes": 1215512}, {"_path": "Library/include/nppi_geometry_transforms.h", "path_type": "hardlink", "sha256": "f0b85eee334f2e66af744cf40fcb723066a40eea6b53921504db10b269ac84bb", "sha256_in_prefix": "f0b85eee334f2e66af744cf40fcb723066a40eea6b53921504db10b269ac84bb", "size_in_bytes": 371721}, {"_path": "Library/include/nppi_linear_transforms.h", "path_type": "hardlink", "sha256": "f6e19ce6c71ee90bdfc00a54843e9480f4ead4b89d88fc1bcbeba8500f34dbc0", "sha256_in_prefix": "f6e19ce6c71ee90bdfc00a54843e9480f4ead4b89d88fc1bcbeba8500f34dbc0", "size_in_bytes": 7562}, {"_path": "Library/include/nppi_morphological_operations.h", "path_type": "hardlink", "sha256": "7a46511328ddbf4c768d21b853c8fdebf9b926b313a6889f9ae97a934debbabc", "sha256_in_prefix": "7a46511328ddbf4c768d21b853c8fdebf9b926b313a6889f9ae97a934debbabc", "size_in_bytes": 148930}, {"_path": "Library/include/nppi_statistics_functions.h", "path_type": "hardlink", "sha256": "34b0a1872ecc0c716f4ad07ed9b841442a5ca292dc24606d353178d0e32a9aab", "sha256_in_prefix": "34b0a1872ecc0c716f4ad07ed9b841442a5ca292dc24606d353178d0e32a9aab", "size_in_bytes": 1294416}, {"_path": "Library/include/nppi_support_functions.h", "path_type": "hardlink", "sha256": "91d029657a147eb293063b641a08cb9c7b84c8b889fbaaf34b3f73346f2be813", "sha256_in_prefix": "91d029657a147eb293063b641a08cb9c7b84c8b889fbaaf34b3f73346f2be813", "size_in_bytes": 14047}, {"_path": "Library/include/nppi_threshold_and_compare_operations.h", "path_type": "hardlink", "sha256": "347a22ea8cfc5e081ea530c3673781fe5cd637c517ab4bbe89c5a9a2a2527228", "sha256_in_prefix": "347a22ea8cfc5e081ea530c3673781fe5cd637c517ab4bbe89c5a9a2a2527228", "size_in_bytes": 276505}, {"_path": "Library/include/npps.h", "path_type": "hardlink", "sha256": "4c1a5bc08059fd8e76c29c8890c40d17158c8226fe054fcf0fdfcbb49251ae6e", "sha256_in_prefix": "4c1a5bc08059fd8e76c29c8890c40d17158c8226fe054fcf0fdfcbb49251ae6e", "size_in_bytes": 3772}, {"_path": "Library/include/npps_arithmetic_and_logical_operations.h", "path_type": "hardlink", "sha256": "20b7461e327aae2d554f4aee7a89deb2675c344d128b1a98a051465a025d8d84", "sha256_in_prefix": "20b7461e327aae2d554f4aee7a89deb2675c344d128b1a98a051465a025d8d84", "size_in_bytes": 396008}, {"_path": "Library/include/npps_conversion_functions.h", "path_type": "hardlink", "sha256": "3795d3c4ef61a9b8b22e39c8ef192c02beeba6e2743ece1e67df48f32b0e1fd1", "sha256_in_prefix": "3795d3c4ef61a9b8b22e39c8ef192c02beeba6e2743ece1e67df48f32b0e1fd1", "size_in_bytes": 102614}, {"_path": "Library/include/npps_filtering_functions.h", "path_type": "hardlink", "sha256": "df77897ee1b7547c59af6b77ab78782eb25432ca416687f42b2cdf420a21afd9", "sha256_in_prefix": "df77897ee1b7547c59af6b77ab78782eb25432ca416687f42b2cdf420a21afd9", "size_in_bytes": 5040}, {"_path": "Library/include/npps_initialization.h", "path_type": "hardlink", "sha256": "1a048e227dad308ef3d8ad207950a9d3bf463263f7239586df14026cecdfeb73", "sha256_in_prefix": "1a048e227dad308ef3d8ad207950a9d3bf463263f7239586df14026cecdfeb73", "size_in_bytes": 30969}, {"_path": "Library/include/npps_statistics_functions.h", "path_type": "hardlink", "sha256": "c4a5dcafe7c5e222fd906e48e8c97dee20d396efa9ef80b141cfb0d8b786db08", "sha256_in_prefix": "c4a5dcafe7c5e222fd906e48e8c97dee20d396efa9ef80b141cfb0d8b786db08", "size_in_bytes": 441809}, {"_path": "Library/include/npps_support_functions.h", "path_type": "hardlink", "sha256": "acd2a0b5c558de13289803048b63ab99c15805f592069e8dad77047ddb85aaa3", "sha256_in_prefix": "acd2a0b5c558de13289803048b63ab99c15805f592069e8dad77047ddb85aaa3", "size_in_bytes": 8265}, {"_path": "Library/lib/nppc.lib", "path_type": "hardlink", "sha256": "8fba44427a0f964acb1f80aec3e65bc90200706430e2c2eed067c1069daa2e7a", "sha256_in_prefix": "8fba44427a0f964acb1f80aec3e65bc90200706430e2c2eed067c1069daa2e7a", "size_in_bytes": 4710}, {"_path": "Library/lib/nppial.lib", "path_type": "hardlink", "sha256": "4534d29cb3421c7d04a96ee5292109511fc5bc7b8136eb3d67cfd1db37c8ada3", "sha256_in_prefix": "4534d29cb3421c7d04a96ee5292109511fc5bc7b8136eb3d67cfd1db37c8ada3", "size_in_bytes": 526998}, {"_path": "Library/lib/nppicc.lib", "path_type": "hardlink", "sha256": "f53f6bef45706a88cba40d89505053984e3960ed09b6847c81a2afa52aa291f4", "sha256_in_prefix": "f53f6bef45706a88cba40d89505053984e3960ed09b6847c81a2afa52aa291f4", "size_in_bytes": 291140}, {"_path": "Library/lib/nppidei.lib", "path_type": "hardlink", "sha256": "d7b04552e4a6e2af68832806fa8a5c052d47e2c71c3eb6e74b46d4715cf1261d", "sha256_in_prefix": "d7b04552e4a6e2af68832806fa8a5c052d47e2c71c3eb6e74b46d4715cf1261d", "size_in_bytes": 380474}, {"_path": "Library/lib/nppif.lib", "path_type": "hardlink", "sha256": "a0223aab9931e02a2f301b2113f0eacea136f0e5f4569d67397ab450abb52096", "sha256_in_prefix": "a0223aab9931e02a2f301b2113f0eacea136f0e5f4569d67397ab450abb52096", "size_in_bytes": 605948}, {"_path": "Library/lib/nppig.lib", "path_type": "hardlink", "sha256": "2e8966fda2f67920ea7c453a27b71486c6df7503c83a5a996cba0ae4eb7556b4", "sha256_in_prefix": "2e8966fda2f67920ea7c453a27b71486c6df7503c83a5a996cba0ae4eb7556b4", "size_in_bytes": 191004}, {"_path": "Library/lib/nppim.lib", "path_type": "hardlink", "sha256": "5c0ea3bc9f54cf75f6694ae30243586537f0f8c865788d3261a4813b3f301e9d", "sha256_in_prefix": "5c0ea3bc9f54cf75f6694ae30243586537f0f8c865788d3261a4813b3f301e9d", "size_in_bytes": 80632}, {"_path": "Library/lib/nppist.lib", "path_type": "hardlink", "sha256": "769309e98910a33abab976b4670d5d03c226e53ca551b7400ea034d060e1b2cf", "sha256_in_prefix": "769309e98910a33abab976b4670d5d03c226e53ca551b7400ea034d060e1b2cf", "size_in_bytes": 1005806}, {"_path": "Library/lib/nppisu.lib", "path_type": "hardlink", "sha256": "9fb2b7a8f8ce9801d156bd0e0e57a301a0dd634e0c85b650e8171936bbae2784", "sha256_in_prefix": "9fb2b7a8f8ce9801d156bd0e0e57a301a0dd634e0c85b650e8171936bbae2784", "size_in_bytes": 8416}, {"_path": "Library/lib/nppitc.lib", "path_type": "hardlink", "sha256": "2acf6af2e4bf73f54277416a7eeeebe41d2a67192b27ef5cf5c50915f0887d84", "sha256_in_prefix": "2acf6af2e4bf73f54277416a7eeeebe41d2a67192b27ef5cf5c50915f0887d84", "size_in_bytes": 117382}, {"_path": "Library/lib/npps.lib", "path_type": "hardlink", "sha256": "3a0deea573aac33e91059169c8866de985e62cb9344177944d4cae50e7b35873", "sha256_in_prefix": "3a0deea573aac33e91059169c8866de985e62cb9344177944d4cae50e7b35873", "size_in_bytes": 465586}], "paths_version": 1}, "requested_spec": "None", "sha256": "40e5815e35f030af714bdb80aea72bcc2094a0f871dc1761d460202c73cfd281", "size": 548907, "subdir": "win-64", "timestamp": 1715372146000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libnpp-dev-12.2.5.30-hd77b12b_1.conda", "version": "12.2.5.30"}