{"build": "h20ee8b7_2", "build_number": 2, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnvfatbin-12.4.127-h20ee8b7_2", "files": ["Library/bin/nvfatbin_120_0.dll"], "fn": "libnvfatbin-12.4.127-h20ee8b7_2.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnvfatbin-12.4.127-h20ee8b7_2", "type": 1}, "md5": "874cbfd2f6c3dd9cd6e6e70c323cccb7", "name": "libnvfatbin", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnvfatbin-12.4.127-h20ee8b7_2.conda", "paths_data": {"paths": [{"_path": "Library/bin/nvfatbin_120_0.dll", "path_type": "hardlink", "sha256": "9632b558107c270dce0543ec5406df838742535a75a915c05bab399f09a4c1e8", "sha256_in_prefix": "9632b558107c270dce0543ec5406df838742535a75a915c05bab399f09a4c1e8", "size_in_bytes": 749056}], "paths_version": 1}, "requested_spec": "None", "sha256": "0a5571d1670d51a1f7d3c4657a43d7ce167ea96a4d591f58a1e980139a38cd00", "size": 319445, "subdir": "win-64", "timestamp": 1715877933000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libnvfatbin-12.4.127-h20ee8b7_2.conda", "version": "12.4.127"}