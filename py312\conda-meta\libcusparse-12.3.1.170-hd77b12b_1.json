{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libnvjitlink >=12.4.127,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcusparse-12.3.1.170-hd77b12b_1", "files": ["Library/bin/cusparse64_12.dll"], "fn": "libcusparse-12.3.1.170-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcusparse-12.3.1.170-hd77b12b_1", "type": 1}, "md5": "acaf4b64d605ada23679158424855c04", "name": "libcusparse", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcusparse-12.3.1.170-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/cusparse64_12.dll", "path_type": "hardlink", "sha256": "f082a4b3896d1b9a3874834fd1385a18b895190d85e8da5ba3187c8c9eea75be", "sha256_in_prefix": "f082a4b3896d1b9a3874834fd1385a18b895190d85e8da5ba3187c8c9eea75be", "size_in_bytes": 275632128}], "paths_version": 1}, "requested_spec": "None", "sha256": "d9acee82e33c1bc77c1789fd2673be1e5e2dd11557f8b1c4d44c9288e6457032", "size": 124146548, "subdir": "win-64", "timestamp": 1715023382000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcusparse-12.3.1.170-hd77b12b_1.conda", "version": "12.3.1.170"}