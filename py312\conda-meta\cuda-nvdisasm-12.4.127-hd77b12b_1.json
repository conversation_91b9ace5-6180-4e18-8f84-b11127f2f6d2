{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-nvdisasm-12.4.127-hd77b12b_1", "files": ["Library/bin/nvdisasm.exe"], "fn": "cuda-nvdisasm-12.4.127-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-nvdisasm-12.4.127-hd77b12b_1", "type": 1}, "md5": "abcd3e9a000b06a450f693df951dac86", "name": "cuda-nvdisasm", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-nvdisasm-12.4.127-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/nvdisasm.exe", "path_type": "hardlink", "sha256": "9a8f5f07f3de2ceeae863ccbe54ebc2604a4c672e9d465a6033bd337b8fab382", "sha256_in_prefix": "9a8f5f07f3de2ceeae863ccbe54ebc2604a4c672e9d465a6033bd337b8fab382", "size_in_bytes": 50694656}], "paths_version": 1}, "requested_spec": "None", "sha256": "5922d7026bc3ff93cee6b6dd46b27a97f67382c69bc175389a777f78d4bfe0f3", "size": 50112591, "subdir": "win-64", "timestamp": 1714771764000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-nvdisasm-12.4.127-hd77b12b_1.conda", "version": "12.4.127"}