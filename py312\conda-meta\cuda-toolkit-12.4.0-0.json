{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/label/cuda-12.4.0/win-64", "constrains": [], "depends": ["cuda-compiler >=12.4.0", "cuda-documentation >=12.4.99", "cuda-libraries >=12.4.0", "cuda-libraries-dev >=12.4.0", "cuda-nvml-dev >=12.4.99", "cuda-tools >=12.4.0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-toolkit-12.4.0-0", "files": [], "fn": "cuda-toolkit-12.4.0-0.tar.bz2", "license": "", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-toolkit-12.4.0-0", "type": 1}, "md5": "24197694b5af157158904ff69cda97cd", "name": "cuda-toolkit", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-toolkit-12.4.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "nvidia/label/cuda-12.4.0::cuda-toolkit", "size": 1803, "subdir": "win-64", "timestamp": 1709168517000, "url": "https://conda.anaconda.org/nvidia/label/cuda-12.4.0/win-64/cuda-toolkit-12.4.0-0.tar.bz2", "version": "12.4.0"}