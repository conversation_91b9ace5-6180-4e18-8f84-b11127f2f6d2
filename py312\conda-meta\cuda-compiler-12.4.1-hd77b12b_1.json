{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vs2017_win-64", "cuda-cuobjdump 12.4.127.*", "cuda-cuxxfilt 12.4.127.*", "cuda-nvcc 12.4.131.*", "cuda-nvprune 12.4.127.*"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-compiler-12.4.1-hd77b12b_1", "files": [], "fn": "cuda-compiler-12.4.1-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-compiler-12.4.1-hd77b12b_1", "type": 1}, "md5": "dc2edcbe1ffd6b3fd7ae729131219c9b", "name": "cuda-compiler", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-compiler-12.4.1-hd77b12b_1.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "63e56e00d61b77263ad1782faf678429a4c5a6ec956c30db8940a94f5c1a4b96", "size": 19898, "subdir": "win-64", "timestamp": 1715799200000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-compiler-12.4.1-hd77b12b_1.conda", "version": "12.4.1"}