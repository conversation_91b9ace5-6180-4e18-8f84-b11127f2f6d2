{"build": "haa95532_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-cudart-dev", "cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-profiler-api-12.4.127-haa95532_1", "files": ["Library/include/cudaProfiler.h", "Library/include/cuda_profiler_api.h"], "fn": "cuda-profiler-api-12.4.127-haa95532_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-profiler-api-12.4.127-haa95532_1", "type": 1}, "md5": "ab1968fce05aa9734f07d3144fddc126", "name": "cuda-profiler-api", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-profiler-api-12.4.127-haa95532_1.conda", "paths_data": {"paths": [{"_path": "Library/include/cudaProfiler.h", "path_type": "hardlink", "sha256": "cb1eb61ea355aac7bd0ba4a4baef987cfd233b1309227fe3b88bb37b58eedadc", "sha256_in_prefix": "cb1eb61ea355aac7bd0ba4a4baef987cfd233b1309227fe3b88bb37b58eedadc", "size_in_bytes": 7235}, {"_path": "Library/include/cuda_profiler_api.h", "path_type": "hardlink", "sha256": "c8973678f0c821e7a3938e0ce37fd6af49c3b4995b215b204c1959002f558322", "sha256_in_prefix": "c8973678f0c821e7a3938e0ce37fd6af49c3b4995b215b204c1959002f558322", "size_in_bytes": 4700}], "paths_version": 1}, "requested_spec": "None", "sha256": "42726f10d291ccdac4f157cba47d66db4a579d25125546a075d6f01007e9d189", "size": 22654, "subdir": "win-64", "timestamp": 1715023033000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-profiler-api-12.4.127-haa95532_1.conda", "version": "12.4.127"}