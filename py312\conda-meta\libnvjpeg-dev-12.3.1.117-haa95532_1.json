{"build": "haa95532_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["cuda-cudart-dev", "cuda-version >=12.4,<12.5.0a0", "libnvjpeg 12.3.1.117 hd77b12b_1"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnvjpeg-dev-12.3.1.117-haa95532_1", "files": ["Library/include/nvjpeg.h", "Library/lib/nvjpeg.lib"], "fn": "libnvjpeg-dev-12.3.1.117-haa95532_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnvjpeg-dev-12.3.1.117-haa95532_1", "type": 1}, "md5": "a7cd9035e2d1bfc99eb74a9326d81274", "name": "libnvjpeg-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnvjpeg-dev-12.3.1.117-haa95532_1.conda", "paths_data": {"paths": [{"_path": "Library/include/nvjpeg.h", "path_type": "hardlink", "sha256": "453b67db2c856c9ae3746efe195de4ec07b5122742c458b3ac11e0ae6b0ab7c8", "sha256_in_prefix": "453b67db2c856c9ae3746efe195de4ec07b5122742c458b3ac11e0ae6b0ab7c8", "size_in_bytes": 34987}, {"_path": "Library/lib/nvjpeg.lib", "path_type": "hardlink", "sha256": "f1ffdc798abb5e26dd9ecc469afcff9fe1fb2eecf22a11ec1ab6ad850edbac2c", "sha256_in_prefix": "f1ffdc798abb5e26dd9ecc469afcff9fe1fb2eecf22a11ec1ab6ad850edbac2c", "size_in_bytes": 22030}], "paths_version": 1}, "requested_spec": "None", "sha256": "1bbf0fcdab360a954c69bc12d60d18660e01a0aa0db00634a4d8057f27e27f51", "size": 31013, "subdir": "win-64", "timestamp": 1715371709000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libnvjpeg-dev-12.3.1.117-haa95532_1.conda", "version": "12.3.1.117"}