{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/noarch", "constrains": [], "depends": ["cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-cudart-static_win-64-12.4.127-hd77b12b_0", "files": ["Library/lib/cudart_static.lib"], "fn": "cuda-cudart-static_win-64-12.4.127-hd77b12b_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-cudart-static_win-64-12.4.127-hd77b12b_0", "type": 1}, "md5": "fafe8156b258562763166118e2b1ad16", "name": "cuda-cudart-static_win-64", "noarch": "generic", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-cudart-static_win-64-12.4.127-hd77b12b_0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "Library/lib/cudart_static.lib", "path_type": "hardlink", "sha256": "497507ce46461d5abfae04dc77a06e01774a886ec471924215e0247239daa822", "sha256_in_prefix": "497507ce46461d5abfae04dc77a06e01774a886ec471924215e0247239daa822", "size_in_bytes": 2738682}], "paths_version": 1}, "requested_spec": "None", "sha256": "cc7c76c3a0bcae567c6307736ee05300d7afbeb4f09008f8373f4e30e17e4bb1", "size": 405497, "subdir": "noarch", "timestamp": 1714768664000, "url": "https://repo.anaconda.com/pkgs/main/noarch/cuda-cudart-static_win-64-12.4.127-hd77b12b_0.conda", "version": "12.4.127"}