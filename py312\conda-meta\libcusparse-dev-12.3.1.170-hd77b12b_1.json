{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libnvjitlink >=12.4.127,<12.5.0a0", "libcusparse 12.3.1.170 hd77b12b_1"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcusparse-dev-12.3.1.170-hd77b12b_1", "files": ["Library/include/cusparse.h", "Library/include/cusparse_v2.h", "Library/lib/cusparse.lib"], "fn": "libcusparse-dev-12.3.1.170-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcusparse-dev-12.3.1.170-hd77b12b_1", "type": 1}, "md5": "0454983c7f3c8a606b6cb2c9a453bf13", "name": "libcusparse-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcusparse-dev-12.3.1.170-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/include/cusparse.h", "path_type": "hardlink", "sha256": "d043924a75caba7e38792b0ae58a5e58d618c520e2a9a33b7c564f48a50dedc7", "sha256_in_prefix": "d043924a75caba7e38792b0ae58a5e58d618c520e2a9a33b7c564f48a50dedc7", "size_in_bytes": 301293}, {"_path": "Library/include/cusparse_v2.h", "path_type": "hardlink", "sha256": "a7a7078e7a404315e0b68d0834edb87094b91129ea3e683dbb8009dbfc29697f", "sha256_in_prefix": "a7a7078e7a404315e0b68d0834edb87094b91129ea3e683dbb8009dbfc29697f", "size_in_bytes": 2641}, {"_path": "Library/lib/cusparse.lib", "path_type": "hardlink", "sha256": "7149422963c971d505832f27603bd8aaac0359e2187dc77c39737129d2a4a681", "sha256_in_prefix": "7149422963c971d505832f27603bd8aaac0359e2187dc77c39737129d2a4a681", "size_in_bytes": 108364}], "paths_version": 1}, "requested_spec": "None", "sha256": "d80d2c3d7f26da2eaf69aab9d53aa1b434f5a6b2a465e07097e0ae408780ccdb", "size": 48770, "subdir": "win-64", "timestamp": 1715023490000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcusparse-dev-12.3.1.170-hd77b12b_1.conda", "version": "12.3.1.170"}