{"build": "h20ee8b7_2", "build_number": 2, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": ["liblibnvfatbin-static >=12.4.127"], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libnvfatbin 12.4.127 h20ee8b7_2"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libnvfatbin-dev-12.4.127-h20ee8b7_2", "files": ["Library/include/nvFatbin.h", "Library/lib/nvfatbin.lib", "Library/lib/nvfatbin_static.lib"], "fn": "libnvfatbin-dev-12.4.127-h20ee8b7_2.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libnvfatbin-dev-12.4.127-h20ee8b7_2", "type": 1}, "md5": "8415035b2392ae56bac54ef35fcc52d1", "name": "libnvfatbin-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libnvfatbin-dev-12.4.127-h20ee8b7_2.conda", "paths_data": {"paths": [{"_path": "Library/include/nvFatbin.h", "path_type": "hardlink", "sha256": "07d74b17467657c51532c6ead8d4d54448676ceefc7782839330b24e843aa191", "sha256_in_prefix": "07d74b17467657c51532c6ead8d4d54448676ceefc7782839330b24e843aa191", "size_in_bytes": 9868}, {"_path": "Library/lib/nvfatbin.lib", "path_type": "hardlink", "sha256": "8868e4c0b54e0027b40ae3a635c73f73cc60f987170a2963645d39642eadcb3f", "sha256_in_prefix": "8868e4c0b54e0027b40ae3a635c73f73cc60f987170a2963645d39642eadcb3f", "size_in_bytes": 3560}, {"_path": "Library/lib/nvfatbin_static.lib", "path_type": "hardlink", "sha256": "4b229fd6f7c3ccdd918b5f9a9e58d2974d67ff81c27d6f3729e463ea088d8ac6", "sha256_in_prefix": "4b229fd6f7c3ccdd918b5f9a9e58d2974d67ff81c27d6f3729e463ea088d8ac6", "size_in_bytes": 4004556}], "paths_version": 1}, "requested_spec": "None", "sha256": "54ad4f3c8900658d66b98856d16bf163d268e94965dbdc393efa8992f2ce33fa", "size": 1088424, "subdir": "win-64", "timestamp": 1715877944000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libnvfatbin-dev-12.4.127-h20ee8b7_2.conda", "version": "12.4.127"}