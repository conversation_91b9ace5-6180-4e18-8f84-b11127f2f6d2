{"build": "haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/noarch", "constrains": [], "depends": ["cuda-version >=12.4,<12.5.0a0"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-crt-dev_win-64-12.4.131-haa95532_0", "files": ["Library/include/crt/common_functions.h", "Library/include/crt/cudacc_ext.h", "Library/include/crt/device_double_functions.h", "Library/include/crt/device_double_functions.hpp", "Library/include/crt/device_functions.h", "Library/include/crt/device_functions.hpp", "Library/include/crt/func_macro.h", "Library/include/crt/host_config.h", "Library/include/crt/host_defines.h", "Library/include/crt/host_runtime.h", "Library/include/crt/math_functions.h", "Library/include/crt/math_functions.hpp", "Library/include/crt/mma.h", "Library/include/crt/mma.hpp", "Library/include/crt/nvfunctional", "Library/include/crt/sm_70_rt.h", "Library/include/crt/sm_70_rt.hpp", "Library/include/crt/sm_80_rt.h", "Library/include/crt/sm_80_rt.hpp", "Library/include/crt/sm_90_rt.h", "Library/include/crt/sm_90_rt.hpp", "Library/include/crt/storage_class.h"], "fn": "cuda-crt-dev_win-64-12.4.131-haa95532_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-crt-dev_win-64-12.4.131-haa95532_0", "type": 1}, "md5": "f43ee82f38cdf65cc184960f91976418", "name": "cuda-crt-dev_win-64", "noarch": "generic", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-crt-dev_win-64-12.4.131-haa95532_0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "Library/include/crt/common_functions.h", "path_type": "hardlink", "sha256": "8123e7afb0ddc439d531a25a1bc59bfb4117ffc8648199f8c32931910b81e3db", "sha256_in_prefix": "8123e7afb0ddc439d531a25a1bc59bfb4117ffc8648199f8c32931910b81e3db", "size_in_bytes": 13869}, {"_path": "Library/include/crt/cudacc_ext.h", "path_type": "hardlink", "sha256": "10b085d6b1fcbe650a75de7455e0f5d38d1be221bacddb3318144b88bc72c5a5", "sha256_in_prefix": "10b085d6b1fcbe650a75de7455e0f5d38d1be221bacddb3318144b88bc72c5a5", "size_in_bytes": 3288}, {"_path": "Library/include/crt/device_double_functions.h", "path_type": "hardlink", "sha256": "72329723f14175d3fcc6e4949b5b32db05b5de804097c2bbbbc94a7264a7dcde", "sha256_in_prefix": "72329723f14175d3fcc6e4949b5b32db05b5de804097c2bbbbc94a7264a7dcde", "size_in_bytes": 41130}, {"_path": "Library/include/crt/device_double_functions.hpp", "path_type": "hardlink", "sha256": "80d741d9fa10a3c8a212f2e9e8bc23306dc30f88d4b2a6016491f872386dc760", "sha256_in_prefix": "80d741d9fa10a3c8a212f2e9e8bc23306dc30f88d4b2a6016491f872386dc760", "size_in_bytes": 8765}, {"_path": "Library/include/crt/device_functions.h", "path_type": "hardlink", "sha256": "24abb1ad8b5f2e06ca3ffaaccd0fe5d7260b5d9fd9bf4db71c0e760b6d90692a", "sha256_in_prefix": "24abb1ad8b5f2e06ca3ffaaccd0fe5d7260b5d9fd9bf4db71c0e760b6d90692a", "size_in_bytes": 140066}, {"_path": "Library/include/crt/device_functions.hpp", "path_type": "hardlink", "sha256": "cffd39dde1ea343bbccd64d81f0916a9f34491de8a70d3de6a6485380fb0e67a", "sha256_in_prefix": "cffd39dde1ea343bbccd64d81f0916a9f34491de8a70d3de6a6485380fb0e67a", "size_in_bytes": 40182}, {"_path": "Library/include/crt/func_macro.h", "path_type": "hardlink", "sha256": "1bb685c45385895321df70d19248a734e13225f4fb5efbfc28fcd21a93b5930d", "sha256_in_prefix": "1bb685c45385895321df70d19248a734e13225f4fb5efbfc28fcd21a93b5930d", "size_in_bytes": 1812}, {"_path": "Library/include/crt/host_config.h", "path_type": "hardlink", "sha256": "da5050c94d6069baf7a4cd492a4fa2fb2fde3f1e98c497e73bac384475a5b5dc", "sha256_in_prefix": "da5050c94d6069baf7a4cd492a4fa2fb2fde3f1e98c497e73bac384475a5b5dc", "size_in_bytes": 12479}, {"_path": "Library/include/crt/host_defines.h", "path_type": "hardlink", "sha256": "234573c04fbd0f9dde853256ca0d121e3702240a9a1c65d5c9e03a8c2a019669", "sha256_in_prefix": "234573c04fbd0f9dde853256ca0d121e3702240a9a1c65d5c9e03a8c2a019669", "size_in_bytes": 10230}, {"_path": "Library/include/crt/host_runtime.h", "path_type": "hardlink", "sha256": "f6dcef2febf6ab6f47ce29067fd2ab4e257e7ec9c54bcc3b0dadfb7e588b0bce", "sha256_in_prefix": "f6dcef2febf6ab6f47ce29067fd2ab4e257e7ec9c54bcc3b0dadfb7e588b0bce", "size_in_bytes": 10590}, {"_path": "Library/include/crt/math_functions.h", "path_type": "hardlink", "sha256": "2110c66d96cb40356ab3757976f6b6bce8fcfd7d8320cc5fe352785119233d87", "sha256_in_prefix": "2110c66d96cb40356ab3757976f6b6bce8fcfd7d8320cc5fe352785119233d87", "size_in_bytes": 408474}, {"_path": "Library/include/crt/math_functions.hpp", "path_type": "hardlink", "sha256": "850897350fcf6abbd00d58b312774aa12098a1674b5327fbbca9018e76cb1b88", "sha256_in_prefix": "850897350fcf6abbd00d58b312774aa12098a1674b5327fbbca9018e76cb1b88", "size_in_bytes": 103605}, {"_path": "Library/include/crt/mma.h", "path_type": "hardlink", "sha256": "34c869f2bef3c27b21bae96353010633ca6aa7205c0d299cdf8d7443f0209b54", "sha256_in_prefix": "34c869f2bef3c27b21bae96353010633ca6aa7205c0d299cdf8d7443f0209b54", "size_in_bytes": 63318}, {"_path": "Library/include/crt/mma.hpp", "path_type": "hardlink", "sha256": "6f0b9c7425503672edc809b9d1584fe7d80219889375c6da8c15eca69812ac5b", "sha256_in_prefix": "6f0b9c7425503672edc809b9d1584fe7d80219889375c6da8c15eca69812ac5b", "size_in_bytes": 67727}, {"_path": "Library/include/crt/nvfunctional", "path_type": "hardlink", "sha256": "f6bd186addac0bcbe6dfeab969ab3cbd744c2553d3f6287739e72b04f207bfd8", "sha256_in_prefix": "f6bd186addac0bcbe6dfeab969ab3cbd744c2553d3f6287739e72b04f207bfd8", "size_in_bytes": 17521}, {"_path": "Library/include/crt/sm_70_rt.h", "path_type": "hardlink", "sha256": "11cfaa9a6987958ba1d573f9fe29b13041bf7fe471a8a245517d210f7d389084", "sha256_in_prefix": "11cfaa9a6987958ba1d573f9fe29b13041bf7fe471a8a245517d210f7d389084", "size_in_bytes": 6975}, {"_path": "Library/include/crt/sm_70_rt.hpp", "path_type": "hardlink", "sha256": "d0d13085e4671530e4f41f81ecf1d4e2101d2b3b3206aae7229b8e013ff55273", "sha256_in_prefix": "d0d13085e4671530e4f41f81ecf1d4e2101d2b3b3206aae7229b8e013ff55273", "size_in_bytes": 8029}, {"_path": "Library/include/crt/sm_80_rt.h", "path_type": "hardlink", "sha256": "f0d3ed12f5419b910a11e6cf6ce79af7d47603be2698d87eee16af67c2a2862f", "sha256_in_prefix": "f0d3ed12f5419b910a11e6cf6ce79af7d47603be2698d87eee16af67c2a2862f", "size_in_bytes": 7907}, {"_path": "Library/include/crt/sm_80_rt.hpp", "path_type": "hardlink", "sha256": "9224440b554c8ecf1357af8f6559a834c26137c4277abaa5948d968a5b233acf", "sha256_in_prefix": "9224440b554c8ecf1357af8f6559a834c26137c4277abaa5948d968a5b233acf", "size_in_bytes": 6853}, {"_path": "Library/include/crt/sm_90_rt.h", "path_type": "hardlink", "sha256": "2443718459320cd1993ec6cf66c7d822fe8866bc9eade5a197ae084884beb3a3", "sha256_in_prefix": "2443718459320cd1993ec6cf66c7d822fe8866bc9eade5a197ae084884beb3a3", "size_in_bytes": 11727}, {"_path": "Library/include/crt/sm_90_rt.hpp", "path_type": "hardlink", "sha256": "461b2c314cf5f1d2bd1782f22fa30882f135c68c8615935eb5360eec39ddc6fc", "sha256_in_prefix": "461b2c314cf5f1d2bd1782f22fa30882f135c68c8615935eb5360eec39ddc6fc", "size_in_bytes": 9476}, {"_path": "Library/include/crt/storage_class.h", "path_type": "hardlink", "sha256": "14b8ddc03a85d579cbc0cb3f1f888c3affa82fad960a1ad01ae689ce6ca008db", "sha256_in_prefix": "14b8ddc03a85d579cbc0cb3f1f888c3affa82fad960a1ad01ae689ce6ca008db", "size_in_bytes": 4933}], "paths_version": 1}, "requested_spec": "None", "sha256": "711b61c46e29ba374768fba998243dcad1540ecfa08faf6c464f0c5dc5963620", "size": 92087, "subdir": "noarch", "timestamp": 1714770020000, "url": "https://repo.anaconda.com/pkgs/main/noarch/cuda-crt-dev_win-64-12.4.131-haa95532_0.conda", "version": "12.4.131"}