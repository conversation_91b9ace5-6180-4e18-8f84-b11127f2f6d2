{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "khronos-opencl-icd-loader >=2024.5.8"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\cuda-opencl-12.4.127-hd77b12b_0", "files": ["Library/etc/OpenCL/vendors/cuda.icd"], "fn": "cuda-opencl-12.4.127-hd77b12b_0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\cuda-opencl-12.4.127-hd77b12b_0", "type": 1}, "md5": "d6706ace3af94205ac15e77639cdd794", "name": "cuda-opencl", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\cuda-opencl-12.4.127-hd77b12b_0.conda", "paths_data": {"paths": [{"_path": "Library/etc/OpenCL/vendors/cuda.icd", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}], "paths_version": 1}, "requested_spec": "None", "sha256": "eb2eb2b52e938aa4e7297d7b40df2b779296de17b9362b75f29e571094a613d6", "size": 20893, "subdir": "win-64", "timestamp": 1715892719000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cuda-opencl-12.4.127-hd77b12b_0.conda", "version": "12.4.127"}