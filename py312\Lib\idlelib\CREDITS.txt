<PERSON>, as well as being the creator of the Python language, is the
original creator of IDLE.  Other contributors prior to Version 0.8 include
<PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>.

Until Python 2.3, IDLE's development was carried out in the SF IDLEfork project. The
objective was to develop a version of IDLE which had an execution environment
which could be initialized prior to each run of user code.
IDLefork was merged into the Python code base in 2003.

The IDLEfork project was initiated by <PERSON>, with some help from <PERSON> and <PERSON>.  <PERSON> wrote the first version of the RPC
code and designed a fast turn-around environment for VPython.  <PERSON> developed
the RPC code and Remote Debugger currently integrated in IDLE.  <PERSON>
contributed considerable time testing and suggesting improvements.

Besides <PERSON> and <PERSON>, the main developers who were active on IDLEfork
are <PERSON>, who implemented the configuration GUI, the new
configuration system, and the About dialog, and <PERSON>, who completed
the integration of the RPC and remote debugger, implemented the threaded
subprocess, and made a number of usability enhancements.

Other contributors include <PERSON>, <PERSON> (Mac integration),
<PERSON> (code check and clean-up), <PERSON> (Mac integration),
<PERSON><PERSON> (Code Context, Call Tips, many other patches), and <PERSON><PERSON> (RPC
integration, debugger integration and persistent breakpoints).

<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>,
<PERSON>, <PERSON> v<PERSON>, <PERSON>end<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> <PERSON>b,
<PERSON> <PERSON>, <PERSON>, <PERSON> <PERSON>e, and Weeble have submitted useful
patches.  Thanks, guys!

<PERSON> contributors since 2005:

- 2005: Tal Einat
- 2010: <PERSON> <PERSON> Reedy (current maintainer)
- 2013: <PERSON> <PERSON>wys
- 2014: Saimadhav Heblikar
- 2015: <PERSON><PERSON>
- 2017: Louie Lu, <PERSON> Sabella, and Serhiy Storchaka

For additional details refer to NEWS.txt and Changelog.

Please contact the IDLE maintainer (<EMAIL>) to have yourself included
here if you are one of those we missed!



