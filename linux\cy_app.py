import sys
import socket
socket.setdefaulttimeout(15000)
import json
import os
os.environ["no_proxy"] = "localhost,127.0.0.1,::1"
import re
from transformers import AutoProcessor, AutoModelForCausalLM
import torch
from PIL import Image, ImageDraw
import random
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import cv2
import io
import uuid

import multiprocessing
import subprocess
import gradio as gr

import glob
import time

import torch
import gc

import shutil
import random
import string
import requests
import shlex


chinese_char_pattern = re.compile(r'[\u4e00-\u9fff]+')
# whether contain chinese character
def contains_chinese(text):
    return bool(chinese_char_pattern.search(text))

from torch import nn
from transformers import (
    # MarianTokenizer,
    # MarianMTModel,
    # MarianConfig,
    AutoModelForSeq2SeqLM,
    AutoTokenizer,
    # T5ForConditionalGeneration,
    )

def trans(sample_text):

    # device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    device = "cpu"
    model_checkpoint = "config_local"
    model = AutoModelForSeq2SeqLM.from_pretrained(model_checkpoint)
    tokenizer = AutoTokenizer.from_pretrained(model_checkpoint)

    model.to(device)

    batch = tokenizer([sample_text], max_length=128, truncation=True, return_tensors="pt")
    for k,v in batch.items():
      batch[k] = v.to(device)
    generated_ids = model.generate(**batch)
    text = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
    return text



def run_example(task_prompt, image, text_input=None):


    model = AutoModelForCausalLM.from_pretrained('ComfyUI/models/LLM/Florence-2-large', trust_remote_code=True).to("cuda").eval()
    processor = AutoProcessor.from_pretrained('ComfyUI/models/LLM/Florence-2-large', trust_remote_code=True)

    prompt = task_prompt if text_input is None else task_prompt + text_input
    inputs = processor(text=prompt, images=image, return_tensors="pt").to("cuda")
    with torch.inference_mode():
        generated_ids = model.generate(**inputs, max_new_tokens=1024, early_stopping=False, do_sample=False, num_beams=3)
    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]

    return processor.post_process_generation(generated_text, task=task_prompt, image_size=(image.size[0], image.size[1]))

def process_image(image):

    
    # task = "More Detailed Caption"

    # task_mapping = {
    #     "More Detailed Caption": ("<MORE_DETAILED_CAPTION>", lambda result: (result['<MORE_DETAILED_CAPTION>'], image))
    # }

    # print(task_mapping[task])


    task = "More Detailed Caption"

    

    

    # 加载图像
    image_path = image
    image = Image.open(image_path)

    def get_more_detailed_caption(result):
        return result['<MORE_DETAILED_CAPTION>'], image
    
    task_mapping = {
        "More Detailed Caption": ("<MORE_DETAILED_CAPTION>", get_more_detailed_caption)
    }

    prompt, process_func = task_mapping[task]
    result = run_example(prompt, image)

    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
    gc.collect()

    return process_func(result)[0].replace("image","video")
    



def generate_random_string(length=5):
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choice(characters) for _ in range(length))



def get_latest_mp4_file(dir_path):
    """
    获取指定目录中创建时间最新的 .mp4 文件。

    参数:
    dir_path (str): 目录路径。

    返回值:
    str: 最新 .mp4 文件的完整路径，如果目录中没有 .mp4 文件则返回 None。
    """
    # 使用 glob 模块查找目录下所有 .mp4 文件
    mp4_files = glob.glob(os.path.join(dir_path, "*.mp4"))

    if not mp4_files:
        return None  # 如果没有 .mp4 文件，返回 None

    # 创建一个列表，包含文件名和对应的创建时间
    files_with_time = [(file, os.path.getctime(file)) for file in mp4_files]

    # 使用 max 函数和 lambda 表达式找到创建时间最晚的文件
    latest_file = max(files_with_time, key=lambda x: x[1])[0]

    return latest_file


def start_service(event):
    """启动一个服务进程."""
    
    # subprocess.Popen(r".\py312\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch", shell=True)
    # cmd = r".\py312\python.exe -s ComfyUI\main.py --windows-standalone-build --disable-auto-launch"
    # process = subprocess.Popen(cmd, shell=True)

    cmd = r"python -s ComfyUI/main.py --disable-auto-launch --listen"

    process = subprocess.Popen(shlex.split(cmd))
    # process.wait()

    specific_string = "8188"

    # while True:
    #     line = process.stdout.readline()
    #     if not line:
    #         break
    #     if specific_string in line:
    #         print(f"Found the specific string: {specific_string}")
    #         event.set()
    #         break
    #     else:
    #         print(line.strip())


    # 等待服务启动
    while True:
        try:
            response = requests.get("http://127.0.0.1:8188")
            if response.status_code == 200:
                event.set()
                break
        except requests.exceptions.ConnectionError:
            pass
        time.sleep(1)

    

    # res.wait()
    # time.sleep(11)
    # event.set()  # 通知 Gradio 进程可以启动#


def start_job(text_z,width,height,length,img):

    # if video_wh == "竖版视频":
    #     width = 480
    #     height = 832
    # else:
    #     width = 832
    #     height = 480

    print(text_z,width,height,length,img)


    if contains_chinese(text_z):
        text_z= trans(text_z)

    print(text_z)

    with open("work/ltxve0.98.json", "r", encoding='utf-8') as f:
        prompt = json.load(f)

    # prompt["303"]["inputs"]["ckpt_name"] = model_z

    prompt["1206"]["inputs"]["image"] = img
    prompt["1338"]["inputs"]["num_frames"] = (length * 24) + 1
    # prompt["3"]["inputs"]["steps"] = step
    # prompt["229"]["inputs"]["int"] = quli
    prompt["6"]["inputs"]["text"] = text_z
    prompt["1872"]["inputs"]["seed"] = random.randint(0, sys.maxsize)
    # prompt["69"]["inputs"]["seed"] = random.randint(0, sys.maxsize)
    # prompt["1945"]["inputs"]["noise_seed"] = random.randint(0, sys.maxsize)
    # prompt["1875"]["inputs"]["seed"] = random.randint(0, sys.maxsize)


    prompt["1893"]["inputs"]["width"] = width
    prompt["1893"]["inputs"]["height"] = height

    # prompt["432"]["inputs"]["width"] = width
    # prompt["432"]["inputs"]["height"] = height
    # prompt["466"]["inputs"]["width"] = width
    # prompt["466"]["inputs"]["height"] = height
    # prompt["432"]["inputs"]["length"] = (length * 24) + 10
    # # prompt["432"]["inputs"]["batch_size"] = step
    # prompt["435"]["inputs"]["steps"] = step
    # prompt["459"]["inputs"]["step"] = round(step / 2)

    # prompt["433"]["inputs"]["noise_seed"] = random.randint(0, sys.maxsize)
    # prompt["460"]["inputs"]["text"] = text_z
    # prompt["463"]["inputs"]["text"] = text_f
    

    # prompt["542"]["inputs"]["rel_l1_thresh"] = teacache


    with open("work/new.json", 'w', encoding='utf-8') as f:
        json.dump(prompt, f, ensure_ascii=False, indent=4)

    try:

        # cmd = fr".\py312\python.exe work/img2vid.py"
        # print(cmd)
        cmd = fr"python work/img2vid.py"
        res = subprocess.run(shlex.split(cmd))
        # res = subprocess.run((cmd))
        # res.wait()
    except Exception as e:
        print(e)
        pass

    one_list = []

    last_video = get_latest_mp4_file("ComfyUI/output")

    bname = os.path.basename(last_video)

    shutil.copy2(last_video,f"生成结果/{bname}")

    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
    gc.collect()

    one_list.append(last_video)

    return last_video,one_list


def change_wh(width,height):

    return height,width



initial_md = """

LTX-VIDEO-0.9.8 13B 满血版 图生视频

<br />

Windows整合包制作:刘悦的技术博客(B站/YouTube 同名) https://t.zsxq.com/IrQPr

"""

quick_prompts = [
    'The girl is smiling, her facial details are clear, her expression is moving, and she is full of charm.',
    '女孩正在微笑，面部细节清晰，表情动人，魅力十足。',
]
quick_prompts = [[x] for x in quick_prompts]

def return_x(x):

    return x[0]

with gr.Blocks() as demo:
    gr.Markdown(initial_md)

    with gr.Accordion("视频制作"):
        with gr.Row():

            with gr.Column():

                img = gr.Image(type="filepath", label="上传图片")
                get_word = gr.Button("根据图片获取提示词")

                

            with gr.Column():
                # model_z = gr.Dropdown(label="模型",choices=["ltxv-2b-0.9.6-dev.safetensors"],value="ltxv-2b-0.9.6-dev.safetensors",interactive=True)
                text_z = gr.Textbox(label="提示词(支持中文提示词)",value="",interactive=True)
                example_quick_prompts = gr.Dataset(samples=quick_prompts, label='提示词示例', samples_per_page=1000, components=[text_z])
                example_quick_prompts.click(return_x, inputs=[example_quick_prompts], outputs=text_z, show_progress=False, queue=False)
                # get_word = gr.Button("根据图片获取提示词")
                # step = gr.Number(label="迭代次数",value=5,interactive=True)
                # device = gr.Textbox(label="显卡编号,注意写你的主力卡，不要写集显的编号",value="cuda:0",interactive=True)
                # shift = gr.Number(label="动作幅度",value=100,interactive=True)
                width = gr.Number(label="视频宽",value=360,interactive=True)
                height = gr.Number(label="视频高",value=713,interactive=True)
                chage_button = gr.Button("交换宽高")

                get_word.click(process_image,[img],[text_z])

                
                chage_button.click(change_wh,[width,height],[width,height])
                # quli = gr.Number(label="视频品质:768,1024,1280,品质越高,速度越慢",value=768,interactive=True)

                # video_wh = gr.Dropdown(choices=["竖版视频","横版视频"], value="竖版视频", label="视频尺寸")

                length = gr.Number(label="视频长度(秒)",value=4,interactive=True)
            # first_button = gr.Button("面部超分")
            make_button = gr.Button("生成视频")

            with gr.Column():

                output_video = gr.Video(label="输出视频",interactive=True,autoplay=True, loop=True)
                one_list = gr.File(interactive=True,label="生成结果下载")
            make_button.click(start_job,inputs=[text_z,width,height,length,img],outputs=[output_video,one_list])

            # get_word.click(process_image,inputs=[img],outputs=[text_z])


def start_gradio(event):
    event.wait()  # 等待 FastAPI 服务启动完成
    demo.launch(server_name="0.0.0.0", server_port=80,inbrowser=True)

if __name__ == "__main__":
    event = multiprocessing.Event()

    fastapi_process = multiprocessing.Process(target=start_service, args=(event,))
    gradio_process = multiprocessing.Process(target=start_gradio, args=(event,))

    fastapi_process.start()
    gradio_process.start()

    fastapi_process.join()
    gradio_process.join()