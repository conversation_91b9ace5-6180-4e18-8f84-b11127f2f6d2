{"build": "hd77b12b_1", "build_number": 1, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0", "cuda-version >=12.4,<12.5.0a0", "libcusolver 11.6.1.9 hd77b12b_1"], "extracted_package_dir": "D:\\anaconda3\\pkgs\\libcusolver-dev-11.6.1.9-hd77b12b_1", "files": ["Library/include/cusolverDn.h", "Library/include/cusolverMg.h", "Library/include/cusolverRf.h", "Library/include/cusolverSp.h", "Library/include/cusolverSp_LOWLEVEL_PREVIEW.h", "Library/include/cusolver_common.h", "Library/lib/cusolver.lib", "Library/lib/cusolverMg.lib"], "fn": "libcusolver-dev-11.6.1.9-hd77b12b_1.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "D:\\anaconda3\\pkgs\\libcusolver-dev-11.6.1.9-hd77b12b_1", "type": 1}, "md5": "da99a94f49b78d1739c331374d56c128", "name": "libcusolver-dev", "package_tarball_full_path": "D:\\anaconda3\\pkgs\\libcusolver-dev-11.6.1.9-hd77b12b_1.conda", "paths_data": {"paths": [{"_path": "Library/include/cusolverDn.h", "path_type": "hardlink", "sha256": "f5e95af9595cb6885fd5a1c359acd1861eeebac96b3ed7790fb8ac6e68413cd5", "sha256_in_prefix": "f5e95af9595cb6885fd5a1c359acd1861eeebac96b3ed7790fb8ac6e68413cd5", "size_in_bytes": 154318}, {"_path": "Library/include/cusolverMg.h", "path_type": "hardlink", "sha256": "900833e0f067ac4fdc8934f6f8333ebe3e0169c3fa6c61faa286330bef01f536", "sha256_in_prefix": "900833e0f067ac4fdc8934f6f8333ebe3e0169c3fa6c61faa286330bef01f536", "size_in_bytes": 11867}, {"_path": "Library/include/cusolverRf.h", "path_type": "hardlink", "sha256": "c4ad1306a770a7dd83824ba3729380322895c0a7203e91fc76ff8369ae4a490f", "sha256_in_prefix": "c4ad1306a770a7dd83824ba3729380322895c0a7203e91fc76ff8369ae4a490f", "size_in_bytes": 14631}, {"_path": "Library/include/cusolverSp.h", "path_type": "hardlink", "sha256": "19fa864954a3347b6b23de5c714db746ce81ca4d9e708edbca2f1f356877879c", "sha256_in_prefix": "19fa864954a3347b6b23de5c714db746ce81ca4d9e708edbca2f1f356877879c", "size_in_bytes": 33484}, {"_path": "Library/include/cusolverSp_LOWLEVEL_PREVIEW.h", "path_type": "hardlink", "sha256": "5a5c28be2d765c9957c46a6cb1a1ed3d20743bf3cb66d8b5abaa82ac70bcb817", "sha256_in_prefix": "5a5c28be2d765c9957c46a6cb1a1ed3d20743bf3cb66d8b5abaa82ac70bcb817", "size_in_bytes": 38602}, {"_path": "Library/include/cusolver_common.h", "path_type": "hardlink", "sha256": "964895745e4e5cad43fb01f121f0b3313d8b6f090251f62d20dddbe3db6baee4", "sha256_in_prefix": "964895745e4e5cad43fb01f121f0b3313d8b6f090251f62d20dddbe3db6baee4", "size_in_bytes": 8992}, {"_path": "Library/lib/cusolver.lib", "path_type": "hardlink", "sha256": "440fe6b50d95f4de6025efad31c5856a217cbe9225fd09fff94b7a531bcff0c0", "sha256_in_prefix": "440fe6b50d95f4de6025efad31c5856a217cbe9225fd09fff94b7a531bcff0c0", "size_in_bytes": 224602}, {"_path": "Library/lib/cusolverMg.lib", "path_type": "hardlink", "sha256": "6a3a19367b9ce7974959e28b0bbfbae1fe809fbe7f60c400dea9a57613a84cc6", "sha256_in_prefix": "6a3a19367b9ce7974959e28b0bbfbae1fe809fbe7f60c400dea9a57613a84cc6", "size_in_bytes": 57764}], "paths_version": 1}, "requested_spec": "None", "sha256": "0bfaa91b64c0bbc2753c809561f7fea3419cb0467e4a89453d4a0d8f4be1bfaa", "size": 66781, "subdir": "win-64", "timestamp": 1715202884000, "url": "https://repo.anaconda.com/pkgs/main/win-64/libcusolver-dev-11.6.1.9-hd77b12b_1.conda", "version": "11.6.1.9"}