{"6": {"inputs": {"text": "a woman is smoking", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "low quality, worst quality, deformed, distorted, disfigured, motion smear, motion artifacts, fused fingers, bad anatomy, weird hand, ugly", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "38": {"inputs": {"clip_name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "type": "ltxv", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "加载CLIP"}}, "44": {"inputs": {"ckpt_name": "ltxv-13b-0.9.8-distilled.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "73": {"inputs": {"sampler_name": "euler_ancestral"}, "class_type": "KSamplerSelect", "_meta": {"title": "K采样器选择"}}, "1206": {"inputs": {"image": "smoke.jpeg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "1241": {"inputs": {"frame_rate": 24.000000000000004, "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "LTXVConditioning", "_meta": {"title": "LTXV条件"}}, "1335": {"inputs": {"vae": ["1872", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "1336": {"inputs": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "ltxv-base", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "images": ["1913", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "1338": {"inputs": {"width": ["1893", 1], "height": ["1893", 2], "num_frames": 97, "optional_cond_indices": "0", "strength": 0.8, "crop": "center", "crf": 30, "blur": 1, "model": ["44", 0], "vae": ["44", 2], "guider": ["1887", 0], "sampler": ["73", 0], "sigmas": ["1876", 0], "noise": ["1507", 0], "optional_cond_images": ["1893", 0]}, "class_type": "LTXVBaseSampler", "_meta": {"title": "🅛🅣🅧 LTXV Base Sampler"}}, "1507": {"inputs": {"noise_seed": 120}, "class_type": "RandomNoise", "_meta": {"title": "随机噪波"}}, "1593": {"inputs": {"factor": 0.25, "latents": ["1691", 0], "reference": ["1338", 0]}, "class_type": "LTXVAdainLatent", "_meta": {"title": "🅛🅣🅧 LTXV Adain Latent"}}, "1691": {"inputs": {"samples": ["1338", 0], "upscale_model": ["1828", 0], "vae": ["44", 2]}, "class_type": "LTXVLatentUpsampler", "_meta": {"title": "🅛🅣🅧 LTXV Latent Upsampler"}}, "1828": {"inputs": {"upscale_model": "ltxv-spatial-upscaler-0.9.8.safetensors", "spatial_upsample": true, "temporal_upsample": false}, "class_type": "LTXVLatentUpsamplerModelLoader", "_meta": {"title": "🅛🅣🅧 LTXV Latent Upsampler Model Loader"}}, "1872": {"inputs": {"timestep": 0.05, "scale": 0.025, "seed": 215396654956775, "vae": ["44", 2]}, "class_type": "Set VAE Decoder Noise", "_meta": {"title": "🅛🅣🅧 Set VAE Decoder Noise"}}, "1875": {"inputs": {"string": "1.0000, 0.9937, 0.9875, 0.9812, 0.9750, 0.9094, 0.7250, 0.4219, 0.0"}, "class_type": "StringToFloatList", "_meta": {"title": "String to Float List"}}, "1876": {"inputs": {"float_list": ["1875", 0]}, "class_type": "FloatToSigmas", "_meta": {"title": "Float To Sigmas"}}, "1887": {"inputs": {"skip_steps_sigma_threshold": 0.9970000000000002, "cfg_star_rescale": true, "sigmas": "1.0, 0.9933, 0.9850, 0.9767, 0.9008, 0.6180", "cfg_values": "1,1,1,1,1,1", "stg_scale_values": "0,0,0,0,0,0", "stg_rescale_values": "1, 1, 1, 1, 1, 1", "stg_layers_indices": "[25], [35], [35], [42], [42], [42]", "model": ["44", 0], "positive": ["1241", 0], "negative": ["1241", 1]}, "class_type": "STGGuiderAdvanced", "_meta": {"title": "🅛🅣🅧 STG Guider Advanced"}}, "1893": {"inputs": {"width": 360, "height": 713, "upscale_method": "nearest-exact", "keep_proportion": "crop", "pad_color": "0, 0, 0", "crop_position": "bottom", "divisible_by": 2, "image": ["1206", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "1894": {"inputs": {"images": ["1893", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "1913": {"inputs": {"tile_size": 1024, "overlap": 128, "temporal_size": 128, "temporal_overlap": 32, "samples": ["1338", 0], "vae": ["1872", 0]}, "class_type": "VAEDecodeTiled", "_meta": {"title": "VAE解码（分块）"}}, "1914": {"inputs": {"purge_cache": true, "purge_models": true, "anything": ["1913", 0]}, "class_type": "LayerUtility: PurgeVRAM", "_meta": {"title": "LayerUtility: Purge VRAM"}}}